<?php

declare(strict_types=1);

/**
 * GraphQL service for product-related queries in curation domain
 * 
 * <AUTHOR> for MTBW-4912
 * @copyright 2024 Wayfair LLC - All rights reserved
 */

namespace App\Infrastructure\Connection\Graphql;

use App\Application\Logger\LoggerTrait;
use App\Infrastructure\Connection\Graphql\GraphClient;
use App\Infrastructure\Connection\Graphql\GraphException;
use App\Infrastructure\Connection\UnleashFeatureToggle\CurationGraphQLDecouplingToggle;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Exception;

class CurationProductGraphService implements LoggerAwareInterface
{
    use LoggerTrait;
    use LoggerAwareTrait;

    private GraphClient $graphClient;
    private CurationGraphQLDecouplingToggle $unleashToggle;

    public const APPLICATION_JSON = 'application/json';

    /**
     * GraphQL query to get SKUs missing product class
     */
    public const GET_SKUS_MISSING_CLASS = '
        query getSkusMissingClass($skus: [String!]!) {
            products(skus: $skus) {
                sku
                hasProductClass
                productClasses {
                    isMasterClass
                }
            }
        }
    ';

    /**
     * GraphQL query to get product details by SKU
     */
    public const GET_PRODUCT_DETAILS = '
        query getProductDetails($sku: String!) {
            product(sku: $sku) {
                sku
                status
                brandCatalogId
                collectionId
                manufacturerId
                productClasses {
                    classId
                    isMasterClass
                }
                additionalInfo {
                    accessoryId
                    dropshipId
                }
            }
        }
    ';

    /**
     * GraphQL query to get active products
     */
    public const GET_ACTIVE_PRODUCTS = '
        query getActiveProducts($skus: [String!]!) {
            products(skus: $skus) {
                sku
                status
                isActive
                additionalInfo {
                    accessoryId
                    dropshipId
                }
                productionTracking {
                    hasActiveTracking
                    quickformId
                }
            }
        }
    ';

    /**
     * GraphQL query to get SKUs with invalid brand catalog
     */
    public const GET_SKUS_INVALID_BRAND_CATALOG = '
        query getSkusInvalidBrandCatalog($skus: [String!]!) {
            products(skus: $skus) {
                sku
                brandCatalogId
            }
        }
    ';

    /**
     * GraphQL query to get SKUs with kit parents and children data
     */
    public const GET_SKUS_WITH_KIT_DATA = '
        query getSkusWithKitData($skus: [String!]!) {
            products(skus: $skus) {
                sku
                kitRelationships {
                    parentSkus
                    childSkus
                }
            }
        }
    ';

    /**
     * Constructor
     *
     * @param GraphClient $graphClient GraphQL client
     * @param CurationGraphQLDecouplingToggle $unleashToggle Unleash toggle for GraphQL decoupling
     */
    public function __construct(GraphClient $graphClient, CurationGraphQLDecouplingToggle $unleashToggle)
    {
        $this->graphClient = $graphClient;
        $this->unleashToggle = $unleashToggle;
    }

    /**
     * Get SKUs that are missing product class (equivalent to get_skus_missing_class SQL query)
     *
     * @param array $skus Array of SKUs to check
     * @return array Array of SKUs missing product class
     * @throws GraphException
     * @throws Exception
     */
    public function getSkusMissingClass(array $skus): array
    {
        if (empty($skus)) {
            return [];
        }

        try {
            $response = $this->sendGraphQLRequest(
                self::GET_SKUS_MISSING_CLASS,
                ['skus' => $skus]
            );

            $missingClassSkus = [];
            if (isset($response['products'])) {
                foreach ($response['products'] as $product) {
                    // Check if product has no master class
                    $hasMasterClass = false;
                    if (isset($product['productClasses'])) {
                        foreach ($product['productClasses'] as $class) {
                            if ($class['isMasterClass'] === true) {
                                $hasMasterClass = true;
                                break;
                            }
                        }
                    }

                    if (!$hasMasterClass) {
                        $missingClassSkus[] = $product['sku'];
                    }
                }
            }

            return $missingClassSkus;
        } catch (Exception $e) {
            $this->error('Failed to get SKUs missing class via GraphQL', [
                'skus' => $skus,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get product details by SKU
     *
     * @param string $sku Product SKU
     * @return array|null Product details or null if not found
     * @throws GraphException
     * @throws Exception
     */
    public function getProductDetails(string $sku): ?array
    {
        try {
            $response = $this->sendGraphQLRequest(
                self::GET_PRODUCT_DETAILS,
                ['sku' => $sku]
            );

            return $response['product'] ?? null;
        } catch (Exception $e) {
            $this->error('Failed to get product details via GraphQL', [
                'sku' => $sku,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Check if products are active (equivalent to get_active_product_query SQL logic)
     *
     * @param array $skus Array of SKUs to check
     * @return array Array of active SKUs
     * @throws GraphException
     * @throws Exception
     */
    public function getActiveProducts(array $skus): array
    {
        if (empty($skus)) {
            return [];
        }

        try {
            $response = $this->sendGraphQLRequest(
                self::GET_ACTIVE_PRODUCTS,
                ['skus' => $skus]
            );

            $activeSkus = [];
            if (isset($response['products'])) {
                foreach ($response['products'] as $product) {
                    if ($this->isProductActive($product)) {
                        $activeSkus[] = $product['sku'];
                    }
                }
            }

            return $activeSkus;
        } catch (Exception $e) {
            $this->error('Failed to get active products via GraphQL', [
                'skus' => $skus,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get SKUs with invalid brand catalog (brandCatalogId = 0)
     *
     * @param array $skus Array of SKUs to check
     * @return array Array of SKUs with invalid brand catalog
     * @throws GraphException
     * @throws Exception
     */
    public function getSkusInvalidBrandCatalog(array $skus): array
    {
        if (empty($skus)) {
            return [];
        }

        try {
            $response = $this->sendGraphQLRequest(
                self::GET_SKUS_INVALID_BRAND_CATALOG,
                ['skus' => $skus]
            );

            $invalidSkus = [];
            if (isset($response['products'])) {
                foreach ($response['products'] as $product) {
                    // Check if brandCatalogId is 0 (invalid)
                    if (($product['brandCatalogId'] ?? null) === 0) {
                        $invalidSkus[] = $product['sku'];
                    }
                }
            }

            return $invalidSkus;
        } catch (Exception $e) {
            $this->error('Failed to get SKUs with invalid brand catalog via GraphQL', [
                'skus' => $skus,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get SKUs with kit parents and children data
     *
     * @param array $skus Array of SKUs to check
     * @return array Array of kit relationship data
     * @throws GraphException
     * @throws Exception
     */
    public function getSkusWithKitData(array $skus): array
    {
        if (empty($skus)) {
            return [];
        }

        try {
            $response = $this->sendGraphQLRequest(
                self::GET_SKUS_WITH_KIT_DATA,
                ['skus' => $skus]
            );

            $kitData = [];
            if (isset($response['products'])) {
                foreach ($response['products'] as $product) {
                    $sku = $product['sku'];
                    $kitRelationships = $product['kitRelationships'] ?? [];

                    // Get first parent and child if they exist
                    $parentSku = !empty($kitRelationships['parentSkus']) ? $kitRelationships['parentSkus'][0] : null;
                    $childSku = !empty($kitRelationships['childSkus']) ? $kitRelationships['childSkus'][0] : null;

                    if ($parentSku || $childSku) {
                        $kitData[] = [
                            'sku' => $sku,
                            'parent_sku' => $parentSku,
                            'child_sku' => $childSku
                        ];
                    }
                }
            }

            return $kitData;
        } catch (Exception $e) {
            $this->error('Failed to get SKUs with kit data via GraphQL', [
                'skus' => $skus,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Send GraphQL request with error handling
     *
     * @param string $query GraphQL query
     * @param array $variables Query variables
     * @param array $options Additional options
     * @return array Response data
     * @throws GraphException
     * @throws Exception
     */
    private function sendGraphQLRequest(string $query, array $variables, array $options = []): array
    {
        try {
            $response = $this->graphClient->getJsonResponseToQuery(
                $query,
                $variables,
                array_merge(
                    [
                        'Accept' => self::APPLICATION_JSON,
                        'Content-Type' => self::APPLICATION_JSON,
                    ],
                    $options
                ),
                true,
                10
            );

            if (array_key_exists('data', $response)) {
                return $response['data'];
            }

            $this->info('No data returned from GraphQL response');
            return [];
        } catch (Exception $error) {
            $this->error('Unable to call curation GraphQL service', [
                'query' => $query,
                'variables' => $variables,
                'error' => $error->getMessage(),
                'trace' => $error->getTrace()
            ]);
            throw $error;
        }
    }

    /**
     * Determine if a product is active based on GraphQL response
     * This replicates the logic from get_active_product_query SQL method
     *
     * @param array $product Product data from GraphQL
     * @return bool True if product is active
     */
    private function isProductActive(array $product): bool
    {
        $status = $product['status'] ?? null;
        $additionalInfo = $product['additionalInfo'] ?? [];
        $productionTracking = $product['productionTracking'] ?? [];

        // Status 2, 4, 6, 13, 18 are active
        if (in_array($status, [2, 4, 6, 13, 18])) {
            return true;
        }

        // Status 3 with specific accessory IDs
        if ($status === 3) {
            $accessoryId = $additionalInfo['accessoryId'] ?? null;
            if (in_array($accessoryId, [1, 2, 4, 7, 12])) {
                return true;
            }
        }

        // Status 7 with specific dropship IDs
        if ($status === 7) {
            $dropshipId = $additionalInfo['dropshipId'] ?? null;
            if (in_array($dropshipId, [22, 23])) {
                return true;
            }
        }

        // Status 1 with active production tracking
        if ($status === 1) {
            $hasActiveTracking = $productionTracking['hasActiveTracking'] ?? false;
            if ($hasActiveTracking) {
                return true;
            }
        }

        return false;
    }
}
