imports:
    # import a whole directory:
    - { resource: services/context_collection.yaml }
    - { resource: services/curation.yaml }

parameters:
    locale: 'en'

    app_name: 'brand-workflows-curation-tool'

    app_root_route: '/d/curation-tool'

    db_driver_options:
        !php/const PDO::SQLSRV_ATTR_QUERY_TIMEOUT: 30

    db_connection_options:
        MultiSubnetFailover: 'true'

    default_ini_path:
        - '/wayfair/etc/wf-config.ini'

    ini_path: '%env(default:default_ini_path:csv:WF_INI_PATH)%'

    default_secret_key_path: '/wayfair/etc/priv/dek/credentials.aes128'
    # predefined parameter in vendor/wayfair/php-core-secrets-symfony-bundle/DependencyInjection/WayfairSecretsExtension.php
    key_path: '%env(default:default_secret_key_path:WF_SECRET_KEY_PATH)%'

    default_secret_credentials_path: '/wayfair/etc/priv/credentials'
    # predefined parameter in vendor/wayfair/php-core-secrets-symfony-bundle/DependencyInjection/WayfairSecretsExtension.php
    secrets_path: '%env(default:default_secret_credentials_path:WF_SECRET_CREDENTIALS_PATH)%'

    verify_ssl: false
    partner_home_auth_timeout: 5 #sec
    product_cache_timeout: 5 #sec

    whitelabel_retry_count: 3
    whitelabel_connection_timeout: 3

    production_tracking_retry_count: 3
    production_tracking_connection_timeout: 3

    suggestion_limit_per_sku: 3

    all_services_lb: '%env(json:wf_env:WF_ALL_SERVICES_LB)%' # read from wf-config.ini json encoded variable `WF_ALL_SERVICES_LB`
    datacenter: '%env(wf_env:WF_POD)%' # read from wf-config.ini variable with the name `WF_POD`, for ex. `sdedsm1`
    fallback_env: '%env(wf_env:WF_ENV)%' # read from wf-config.ini variable with the name `WF_POD`, for ex. `dev`
    wf_env: '%env(default:fallback_env:WF_ENV)%'

    # Production Tracking API
    production-tracking-api-key:    '%env(secret:curation-production-tracking-api-client-id)'
    production-tracking-api-secret: '%env(secret:curation-production-tracking-api-client-secret)'

    cost-service-api-client-id:     '%env(secret:COST_API_CLIENT_ID)%'
    cost-service-api-client-secret: '%env(secret:COST_API_CLIENT_SECRET)%'

    wl-nextgen-client-id:     '%env(secret:WL_API_CLIENT_ID)%'
    wl-nextgen-client-secret: '%env(secret:WL_API_CLIENT_SECRET)%'

    talent-api-client-id:     '%env(TALENT_API_CLIENT_ID)%'
    talent-api-client-secret: '%env(secret:TALENT_API_CLIENT_SECRET)%'

    curation-db-user-password: '%env(secret:CURATION_DB_USER_PASSWORD_POSTGRES)%'

    base_url:
        dev:  'https://partnerswayfaircom.csnzoo.com/'
        prod: 'https://partners.wayfair.com/'

    wf_sso_url:
        dev:  'https://ssoauthwayfaircom.csnzoo.com/'
        prod: 'https://sso.auth.wayfair.com/'

    production_tracking_auth_url: '%wf_sso_url%'
    wl_nextgen_api_auth_url:      '%wf_sso_url%'

    production_tracking_api_url:
        dev:  'https://apiwayfaircom.csnzoo.com/'
        prod: 'https://api.wayfair.com/'

    feature_toggle_cache_ttl:    120 # 2 min
    db_code_discovery_cache_ttl: 300 # 5 min
    wl_nextgen_api_cache_ttl:    600 # 10 min

services:
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $curation_section_service: '@curation_section_service'
            $curation_section_service_with_collisions: '@curation_section_service_with_collisions'

    Doctrine\DBAL\Platforms\SQLServer2012Platform: ~

    App\Application\Controller\:
        resource: '../src/Application/Controller'
        tags: ['controller.service_arguments']

    App\Application\Service\:
        resource: '../src/Application/Service'

    App\Domain\Service\:
        resource: '../src/Domain/Service'

    App\Infrastructure\:
        resource: '../src/Infrastructure'
        exclude: ['../src/Infrastructure/{View}', '../src/Infrastructure/**/*Exception.php']

    WF\PartnerHome\UserState\Factory: ~

    WF\PartnerHome\UserState\UserStateInterface:
        factory: ['@WF\PartnerHome\UserState\Factory', 'build']
        arguments:
            - !php/const WF\PartnerHome\UserState\Factory\SettingKeysEnum::MODE:   '%wf_env%'
              !php/const WF\PartnerHome\UserState\Factory\SettingKeysEnum::CLIENT: '@Psr\Http\Client\ClientInterface'
              !php/const WF\PartnerHome\UserState\Factory\SettingKeysEnum::LOGGER: '@Psr\Log\LoggerInterface'

    App\Application\Helper\ContentTypeHelper: ~

    App\Application\Helper\ContentTypeHelperInterface: '@App\Application\Helper\ContentTypeHelper'

    App\Application\Service\ViewRenderer:
        arguments: ['%wf_env%']

    ######################## Event Listeners #########################

    App\Application\EventListener\RedirectToMonolithEventListener:
        arguments:
            $appRootRoute: '%app_root_route%'
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.request', method: 'onRequest', priority: -200 }

    App\Application\EventListener\CheckToolDisabledEventListener:
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.controller', method: 'onKernelController', priority: -100 }

    App\Application\EventListener\ToolDisabledExceptionListener:
        arguments:
            - '@App\Application\Service\ViewRenderer'
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.exception', method: 'onKernelException', priority: 100 }

    App\Application\EventListener\AccessDeniedExceptionListener:
        arguments:
            - '@App\Application\Service\ViewRenderer'
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.exception', method: 'onKernelException', priority: -100 }

    App\Application\EventListener\NotAuthorizedExceptionListener:
        arguments:
            - '@App\Application\Service\ViewRenderer'
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.exception', method: 'onKernelException', priority: 100 }

    App\Application\EventListener\BatchActionNotMatchStatusExceptionListener:
        arguments:
            - '@router.default'
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.exception', method: 'onKernelException' }

    App\Application\EventListener\WebpackLayoutViewListener:
        arguments:
            - '@App\Application\Service\ViewRenderer'
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.view', method: 'onKernelView' }

    ######################## Param Converters ########################

    App\Application\Request\ParamConverter\CurationParamConverter:
        tags:
            - { name: 'request.param_converter', converter: 'curation' }


    App\Application\Request\ParamConverter\CompletionBatchParamConverter:
        tags:
            - { name: 'request.param_converter', converter: 'curation_completion' }

    ######################### Metrics CONFIG #########################

    WF\Db\ConnectionFactoryInterface:
        factory: ['WF\Db\ConnectionFactory', 'create']
        arguments:
            - '@Psr\Log\LoggerInterface'
            - '@db_code_discovery_cache'
            - '@WF\Contracts\EnvConfig\EnvConfigInterface'
            - '@WF\Secrets\SecretProviderInterface'
        calls:
            - setStartQueryLogPercentage: [ 100 ]
            - setSlowQueryReportingThreshold: [ 5 ]

    # Connection Creation
    db_merch_connection_settings:
        class: 'App\Infrastructure\Connection\Driver\ConnectionProperties'
        arguments:
            - 'merch'
            - '%env(resolve:DB_USER)%'
            - '%env(resolve:DB_PASSWORD_SECRET_NAME)%'
            - '%app_name%'
            - ''
            - '%db_connection_options%'
            - '%db_driver_options%'

    App\Infrastructure\Connection\MerchConnection:
        arguments:
            - '@WF\Db\ConnectionFactoryInterface'
            - '@db_merch_connection_settings'

    # Lazy load proxy to named connection
    db_product_connection_settings:
        class: 'App\Infrastructure\Connection\Driver\ConnectionProperties'
        arguments:
            - 'ptrw' # DB connection code
            - '%env(resolve:DB_USER)%'
            - '%env(resolve:DB_PASSWORD_SECRET_NAME)%'
            - '%app_name%'
            - ''
            - '%db_connection_options%'
            - '%db_driver_options%'

    # Lazy load proxy to named connection
    App\Infrastructure\Connection\ProductConnection:
        arguments:
            - '@WF\Db\ConnectionFactoryInterface'
            - '@db_product_connection_settings'

    db_internal_tools_connection_settings:
        class: 'App\Infrastructure\Connection\Driver\ConnectionProperties'
        arguments:
            - 'intt' # DB connection code
            - '%env(resolve:DB_USER)%'
            - '%env(resolve:DB_PASSWORD_SECRET_NAME)%'
            - '%app_name%'
            - ''
            - '%db_connection_options%'
            - '%db_driver_options%'

    # Lazy load proxy to named connection
    App\Infrastructure\Connection\InternalToolsConnection:
        arguments:
            - '@WF\Db\ConnectionFactoryInterface'
            - '@db_internal_tools_connection_settings'

    db_order_connection_settings:
        class: 'App\Infrastructure\Connection\Driver\ConnectionProperties'
        arguments:
            - 'ot' # DB connection code
            - '%env(resolve:DB_USER)%'
            - '%env(resolve:DB_PASSWORD_SECRET_NAME)%'
            - '%app_name%'
            - ''
            - '%db_connection_options%'
            - '%db_driver_options%'
    # Lazy load proxy to named connection
    App\Infrastructure\Connection\OrderConnection:
        arguments:
            - '@WF\Db\ConnectionFactoryInterface'
            - '@db_order_connection_settings'

    App\Infrastructure\Connection\ProductConnectionAdapter:
        arguments:
            - '@App\Infrastructure\Connection\ProductConnection'

    App\Infrastructure\Connection\MerchConnectionAdapter:
        arguments:
            - '@App\Infrastructure\Connection\MerchConnection'

    # psql to named connection
    db_psql_connection_settings:
        class: 'App\Infrastructure\Connection\Driver\ConnectionPsqlProperties'
        arguments:
            - 'pdo_pgsql' # DB connection code
            - 'app'
            - '%env(secret:resolve:CURATION_DB_USER_PASSWORD_POSTGRES)%'

    # Postgres to named connection
    App\Infrastructure\Connection\PostgresConnection:
        arguments:
            - '@db_psql_connection_settings'

    ######################### DB CONFIG #########################

    WF\Metrics\MetricsContainerInterface:
        factory: ['\WF\Metrics\MetricsContainerFactory', 'create']
        arguments:
            $envConfig: '@WF\Contracts\EnvConfig\EnvConfigInterface'
            $namePrefix: '%app_name%.'

    WF\Metrics\MetricsSenderInterface:
        factory: ['@WF\Metrics\MetricsContainerInterface', 'getMetricsSender']

    ######################### Feature Toggle CONFIG #########################

    WF\FeatureToggle\PuRESTLocation:
        arguments:
            - '@=parameter("all_services_lb")[parameter("datacenter")]'

    cache_interval_3m:
        class: 'DateInterval'
        arguments:
            - 'PT3M'# 3 mins

    feature_toggle_cache_provider:
        class: 'WF\FeatureToggle\Cache\CacheProvider'
        arguments:
            - '@feature_toggle_cache'
            - '@cache_interval_3m'

    WF\FeatureToggle\FeatureTogglesInterface:
        factory: ['WF\FeatureToggle\FeatureTogglesFactory', 'create']
        arguments:
            - '@WF\FeatureToggle\PuRESTLocation'
            - '@feature_toggle_cache_provider'

    ######################### ENV CONFIG #########################

    WF\EnvConfig\EnvConfig:
        alias: 'WF\Contracts\EnvConfig\EnvConfigInterface'

    WF\Contracts\EnvConfig\EnvConfigInterface:
        factory: ['WF\EnvConfig\EnvConfigFactory', 'create']
        arguments:
            $iniPaths: '%ini_path%'

    ##################### Translation ############################

    App\Application\Translation\Translator:
        arguments: ['@translator.default']

    App\Application\Translation\TranslatorInterface: '@App\Application\Translation\Translator'

    ############## Override default secret location ##############

    WF\Secrets\SecretContainerInterface:
        factory: ['WF\Secrets\SecretContainerFactory', 'create']
        arguments:
            - '%key_path%'
            - '%secrets_path%'

    ############## Cache config ##############

   # Psr\SimpleCache\CacheInterface:
   #     class: 'Symfony\Component\Cache\Psr16Cache'
   #     arguments:
   #         - '@Symfony\Component\Cache\Adapter\PhpFilesAdapter'
   #
   # Symfony\Component\Cache\Adapter\PhpFilesAdapter:
   #     arguments:
   #         - '%app_name%.default' # default namespace
   #         - 300 # 5 min

    feature_toggle_cache:
        class: 'Symfony\Component\Cache\Psr16Cache'
        arguments:
            - '@feature_toggle_cache_adapter'

    feature_toggle_cache_adapter:
        class: 'Symfony\Component\Cache\Adapter\PhpFilesAdapter'
        arguments:
            - '%app_name%.feature-toggle' # default namespace
            - '%feature_toggle_cache_ttl%'

    db_code_discovery_cache:
        class: 'Symfony\Component\Cache\Psr16Cache'
        arguments:
            - '@db_code_discovery_cache_adapter'

    db_code_discovery_cache_adapter:
        class: 'Symfony\Component\Cache\Adapter\PhpFilesAdapter'
        arguments:
            - '%app_name%.db-code' # default namespace
            - '%db_code_discovery_cache_ttl%'

    ######################### Monolith #######################################

    WF\Shared\Intl\Translation\Translate: ~

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\:
        resource: '../libs/Extranet/Classes/Catalog/Exclusivity/Curation_Tool'
        exclude:
            - '../libs/Extranet/Classes/Catalog/Exclusivity/Curation_Tool/Completion/Entities/**'
            - '../libs/Extranet/Classes/Catalog/Exclusivity/Curation_Tool/External/Style_Suggestion_Api_Proxy.php'
            - '../libs/Extranet/Classes/Catalog/Exclusivity/Curation_Tool/Loading/{Curation_Item,Curation_Item_Group,Investment_Sku_Metric,Investment_Sku_Metric_Collection,Rebrand_Project,Rejection_Reason,Section,Suggested_Style_Rejection_Reason}.php'
            - '../libs/Extranet/Classes/Catalog/Exclusivity/**/{Exception,Enum,Entity}/**'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Batch\:
        resource: '../libs/Extranet/Classes/Catalog/Exclusivity/Curation_Batch'
    #

    WF\Shared\Models\ProductManagement\WorldRegion\:
        resource: '../libs/Shared/Models/ProductManagement/WorldRegion'

    WF\Shared\Classes\ProductManagement\:
        resource: '../libs/Shared/Classes/ProductManagement'

    #WF\Shared\Curation\:
    #    resource: '../libs/Shared/Curation'

    WF\Shared\Classes\Product\:
        resource: '../libs/Shared/Classes/Product'

    WF\Shared\ProductManagement\:
        resource: '../libs/Shared/ProductManagement'

    # DAO

    WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\:
        resource: '../libs/Extranet/DAOs/Catalog/Exclusivity/Curation_Tool'
        exclude: ['../libs/Extranet/DAOs/**/*Exception.php']

    WF\Shared\DAOs\Product\Media\Curation_Tool\:
        resource: '../libs/Shared/DAOs/Product/Media/Curation_Tool'
        exclude: ['../libs/Shared/DAOs/Product/Media/**/*Exception.php']

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_Storage: '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_DAO'

    WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO:
        arguments:
            - '@App\Infrastructure\Connection\ProductConnection'
            - '@logger'
            - '@=service("WF\\FeatureToggle\\FeatureTogglesInterface").isEnabled("merch_curation_use_strict_class_data")'
            - '@WF\Shared\Curation\Api\Cost\CostApiService'

    WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_DAO: ~
    WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_Postgres_DAO: ~
    WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Curator_Loader_DAO: ~

    WF\Shared\DAOs\Product\Media\Curation_Tool\SKU_Selection\Curation_Batch_DAO: ~

    WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_Postgres_DAO: ~
    WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Curation_Batch_DAO: ~
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Batch\Factory: ~
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Data_Storage: '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_DAO'
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Batch_Status_Updater_Storage: '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_DAO'
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Storage: '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_DAO'
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Verification_Item_Storage: '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Batch\Batch_Management_DAO'

    _instanceof:
        WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validation:
            tags: ['curation_data_consistency_validation']

        WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Curation_Saver:
            tags: ['automatic_curation_savers']

        WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\SKU_Eligibility_Requirement_Interface:
            tags: ['sku_eligibility_requirement']

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validator:
        bind:
            $validations: !tagged_iterator 'curation_data_consistency_validation'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Data_Consistency_Validation_DAO:
        arguments:
            - '@App\Infrastructure\Connection\ProductConnection'
            - '@App\Infrastructure\Connection\Graphql\CurationProductGraphService'
            - '@App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface'
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validator_Storage: '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Data_Consistency_Validation_DAO'

    WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Assortment_Decision\Exclusivity_Assortment_Tool_Decision_Loader: ~
    WF\Shared\Classes\Contracts\Assortment_Decision\Assortment_Decision_DTO_Loader: '@WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Assortment_Decision\Exclusivity_Assortment_Tool_Decision_Loader'

    WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Requirement_Container:
        arguments:
            - '@WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Loader_Eligibility_Requirement'
            - '@WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Loader_Right_Product_Status_Requirement'
            - '@WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Null_Master_Core_Class_Requirement'
            - '@WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Null_Holdout_Manufacturer_Requirement'
            - '@WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Null_Perigold_Only_Requirement'
            - '@WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements\Loader_Holdout_Manufacturer_Part_Requirement'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Rejected:
        arguments:
            - '@WF\Shared\Classes\ProductManagement\Mailer\Logging_Mailer'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_Service'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Service'
            - '@=parameter("base_url")[parameter("wf_env")]'
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Notify_Curation_Updated:
        arguments:
            - '@WF\Shared\Classes\ProductManagement\Mailer\Logging_Mailer'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_Employee_Service'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Completion\Completion_QA_Batch_SKU_Service'
            - '@=parameter("base_url")[parameter("wf_env")]'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_DAO: ~
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Automation_Item_Storage: '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Automation_Item_DAO'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Factory:
        arguments:
            - '@WF\Shared\Classes\ProductManagement\Utils\Merger'
            - '@App\Domain\Service\Loading\CurationItemFactory'
            - '@=service("WF\\FeatureToggle\\FeatureTogglesInterface").isEnabled("mtbw_enable_curation_collision_loading_limitation")'


    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Collision_Loader:
        arguments:
            - '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO'
            - '@App\Domain\Service\Loading\CurationItemFactory'
            - '@WF\Shared\Classes\ProductManagement\Curation\Collider\Curation_Collection_Collider'
            - '@WF\Shared\Classes\ProductManagement\Curation\Collider\Curation_Kit_Collider'
            - '@WF\Shared\Curation\Api\Api_Curation'
            - '@WF\Shared\SLO\SLOFactory'
            - '@logger'
            - '@=service("WF\\FeatureToggle\\FeatureTogglesInterface").isEnabled("enable_context_collection_api_for_collisions")'
            - '@=service("WF\\FeatureToggle\\FeatureTogglesInterface").isEnabled("enable_curation_collision_calculation_optimization")'
            - '@=service("WF\\FeatureToggle\\FeatureTogglesInterface").isEnabled("merch_curation_use_eligibility_filter_collision")'
            - '@WF\Shared\Curation\Api\ApiProductContextCollectionNextgen'

    curation_item_group_loader_with_collisions:
        class: 'WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Loader'
        arguments:
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Collision_Loader'
            - '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Factory'
            - '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO'
            - '@App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface'
            - '@logger'

    curation_item_group_loader:
        class: 'WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Loader'
        arguments:
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Loader'
            - '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Group_Factory'
            - '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO'
            - '@App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface'
            - '@logger'

    curation_section_loader:
        class: 'WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader'
        arguments:
            - '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Image_Loader'
            - '@curation_item_group_loader'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper'
            - '@WF\Shared\Models\ProductManagement\WorldRegion\World_Region_EU'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Price_Loader'
            - '@App\Domain\Service\Loading\CurationItemFactory'
            - '@App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface'
            - '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO'
            - '@logger'

    curation_section_loader_with_collisions:
        class: 'WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Loader'
        arguments:
            - '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Curation_Tool_DAO'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Context_SKUs\Context_Data_Service'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Assortment_Curation_Decision\Assortment_Curation_Decision_Loader'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Image_Loader'
            - '@curation_item_group_loader_with_collisions'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Kits\Kit_Parent_Grouper'
            - '@WF\Shared\Models\ProductManagement\WorldRegion\World_Region_EU'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Price_Loader'
            - '@App\Domain\Service\Loading\CurationItemFactory'
            - '@App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface'
            - '@WF\Extranet\DAOs\Catalog\Exclusivity\Curation_Tool\Postgres_DAO\Curation_Tool_Postgres_DAO'
            - '@logger'

    curation_section_service:
        class: 'WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Section_Service'
        public: true
        arguments:
            - '@curation_section_loader'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Additional_Data_Loader'
            - []
            - '@logger'

    curation_section_service_with_collisions:
        class: 'WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Section_Service'
        public: true
        arguments:
            - '@curation_section_loader_with_collisions'
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Section_Additional_Data_Loader'
            - !tagged_iterator  'automatic_curation_savers'
            - '@logger'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage\Automatic_Suggestion_DAO:
        arguments:
            - '@App\Infrastructure\Connection\ProductConnection'
            - '%suggestion_limit_per_sku%'
            - '@logger'
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Storage\Automatic_Suggestion_Postgres_DAO:
        arguments:
            - '@App\Infrastructure\Connection\ProductConnection'
            - '@App\Infrastructure\Connection\PostgresConnection'
            - '%suggestion_limit_per_sku%'
            - '@logger'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Storage\Curation_Only_Batches_DAO:
        arguments:
            - '@App\Infrastructure\Connection\ProductConnection'
            - '@logger'
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Only_Batches\Storage\Curation_Only_Batches_Postgres_DAO:
        arguments:
            - '@App\Infrastructure\Connection\PostgresConnection'
            - '@logger'

    WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\White_Label_Batch_DAO: ~
    WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\White_Label_Batch_Postgres_DAO: ~
    WF\Shared\DAOs\Product\Media\Batch_Verification_MSSQL_DAO: ~
    WF\Shared\DAOs\Product\Media\Batch_Verification_Postgres_DAO: ~
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Downstream\Storage\Batch_Evaluation_Tracker_DAO: ~
    WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_DAO: ~
    WF\Shared\DAOs\ProductManagement\WhiteLabel\Batching\Batch_SKU_Postgresql_DAO: ~

    WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Persister: ~
    WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Persister_Interface: '@WF\Shared\Classes\ProductManagement\WhiteLabel\Batching\Batch_Persister'

    WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluator: ~
    WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluator_Interface: '@WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluator'

    WF\Shared\SLO\SLOFactory: ~

    WF\Shared\Logging\Logger: ~

    WF\Extranet\Classes\Curation\Redirect\Redirect_Product_Page_URL_Helper: ~

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Loader_Interface: '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Item_Loader'

    WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Collection_Microservice_Retriever:
        arguments:
            - '@WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Service'
            - '@WF\Shared\Curation\Api\Api_Product_Context_Collection'
            - '@logger'
            - '@=service("WF\\FeatureToggle\\FeatureTogglesInterface").isEnabled("curation_fetch_context_xn_id_from_microservice")'

    WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Collection_Retrieve_Interface: '@WF\Shared\Classes\Product\Media\Curation_Tool\Service\Context_SKU_Collection_Microservice_Retriever'

    WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_Legacy_DAO: ~
    WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO: ~
    WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_Postgres_DAO: ~

    App\Domain\Service\Collider\CurationColliderDAOFactory:
        arguments:
            - '@WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO'
            - '@WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_Legacy_DAO'

    curation_collider_dao:
        class: 'WF\Shared\DAOs\ProductManagement\Curation\Curation_Collider_DAO'
        factory: ['@App\Domain\Service\Collider\CurationColliderDAOFactory', 'getCurationColliderDAO']
        arguments:
            - '@=service("WF\\FeatureToggle\\FeatureTogglesInterface").isEnabled("collisions_replace_perigold_with_white_label_sku")'

    WF\Shared\Classes\ProductManagement\Curation\Collider\Curation_Collection_Collider:
        arguments:
            - '@curation_collider_dao'

    WF\Shared\DAOs\ProductManagement\Curation\Curation_Option_Combination_Loader_DAO: ~

    WF\Shared\DAOs\ProductManagement\WhiteLabel\Kit_Collision_SKU_DAO: ~

    WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Storage\Assortment_Tool_Candidate_DAO: ~
    WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Assortment_Decision\Exclusivity_Assortment_Tool_Decision_Loader_Storage: '@WF\Extranet\Classes\Curation\Exclusivity_Assortment_Tool\Storage\Assortment_Tool_Candidate_DAO'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Automatic_Curation\Automatic_Style_Curation_Saver: ~

    WF\Shared\Curation\Api\Exclusivity_Assortment\Exclusivity_Assortment_Service: ~
    WF\Shared\Curation\Api\Api_Exclusivity_Assortment: '@WF\Shared\Curation\Api\Exclusivity_Assortment\Exclusivity_Assortment_Service'

    WF\Shared\Curation\Api\Predicted_Winner\Predicted_Winner_Api_Client:
        arguments:
            - '@logger'
            - '%wf_env%'

    WF\Shared\Curation\Api\Api_Predicted_Winner: '@WF\Shared\Curation\Api\Predicted_Winner\Predicted_Winner_Api_Client'

    WF\Shared\Curation\Api\Exclusivity_Assortment\Storage\Exclusivity_Assortment_Api_Dao: ~
    WF\Shared\Curation\Api\Exclusivity_Assortment\Exclusivity_Assortment_Loader_Interface: '@WF\Shared\Curation\Api\Exclusivity_Assortment\Storage\Exclusivity_Assortment_Api_Dao'

    WF\Shared\DAOs\Product\Media\Curation_Tool\SKU_Selection\Curation_Decision_Logging_BigQuery_DAO:
        arguments:
            $environment: '%wf_env%'

    # Style Suggestion

    WF\Shared\Curation\Api\Style_Suggestion\Style_Suggestion_Api_Client:
        arguments:
            $environment: '%wf_env%'

    WF\Shared\Curation\Api\Api_Style_Suggestion: '@WF\Shared\Curation\Api\Style_Suggestion\Style_Suggestion_Api_Client'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\External\Style_Suggestion_Api_Proxy:
        arguments:
            - '@WF\Shared\Curation\Api\Style_Suggestion\Style_Suggestion_Api_Client'
            - '%suggestion_limit_per_sku%'
            - '@logger'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Style_Suggestion_Storage: '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\External\Style_Suggestion_Api_Proxy'

    GuzzleRetry\GuzzleRetryMiddleware:
        factory: ['GuzzleRetry\GuzzleRetryMiddleware', 'factory']
        arguments:
            - max_retry_attempts: 3
              retry_on_status: [500, 503, 429, 400, 404]
              default_retry_multiplier: 0.5
              retry_on_timeout: true

    GuzzleHttp\HandlerStack:
        factory: ['GuzzleHttp\HandlerStack', 'create']
        calls:
            - push: [ '@GuzzleRetry\GuzzleRetryMiddleware' ]

    # Generic Guzzle Client
    GuzzleHttp\Client:
        arguments:
            - verify: '%verify_ssl%'
              handler: '@GuzzleHttp\HandlerStack'
              timeout: '%partner_home_auth_timeout%' # sec

    Http\Adapter\Guzzle6\Client:
        arguments:
            - '@GuzzleHttp\Client'

    ### Assortment Library start ###

    # Aletheia API client
    WF\Curation\ExclusivityAssortment\Domain\Price\DAO\CurationOptionCombinationLoaderDAO:
        arguments:
            - '@App\Infrastructure\Connection\ProductConnectionAdapter'

    WF\Curation\ExclusivityAssortment\Domain\Price\Services\PriceOptionLoader: ~

    WF\Curation\ExclusivityAssortment\Domain\Price\Factory\:
        resource: '../vendor/wayfair/brand-workflows-assortment-library/src/Domain/Price/Factory'

    WF\Curation\ExclusivityAssortment\Domain\Price\Services\:
        resource: '../vendor/wayfair/brand-workflows-assortment-library/src/Domain/Price/Services'

    Psr\Http\Message\UriFactoryInterface: '@WF\Curation\ExclusivityAssortment\Domain\Price\Factory\UriFactory'
    Psr\Http\Client\ClientInterface: '@Http\Adapter\Guzzle6\Client'
    Psr\Http\Message\RequestFactoryInterface: '@WF\Curation\ExclusivityAssortment\Domain\Price\Factory\RequestFactory'
    Psr\Http\Message\StreamFactoryInterface: '@WF\Curation\ExclusivityAssortment\Domain\Price\Factory\StreamFactory'

    WF\Curation\ExclusivityAssortment\Domain\Price\Client\AletheiaApiClient:
        bind:
            $environment: '%wf_env%'

    WF\Curation\ExclusivityAssortment\Domain\Price\DataTransformer\:
        resource: '../vendor/wayfair/brand-workflows-assortment-library/src/Domain/Price/DataTransformer'

    WF\Curation\ExclusivityAssortment\Domain\Price\DataTransformer\UnitSalePriceDataTransformer:    ~
    WF\Curation\ExclusivityAssortment\Domain\Price\DataTransformer\UnitPriceOptionsDataTransformer: ~
    WF\Curation\ExclusivityAssortment\Domain\Price\DataTransformer\EmptyPriceDataTransformer:       ~

    WF\Curation\ExclusivityAssortment\Domain\Price\DataTransformer\DataTransformationManager:
        arguments:
            -
              - '@WF\Curation\ExclusivityAssortment\Domain\Price\DataTransformer\UnitSalePriceDataTransformer'
              - '@WF\Curation\ExclusivityAssortment\Domain\Price\DataTransformer\UnitPriceOptionsDataTransformer'
              - '@WF\Curation\ExclusivityAssortment\Domain\Price\DataTransformer\EmptyPriceDataTransformer'

    # TODO temp block - need to remove after testing
    WF\Curation\ExclusivityAssortment\Infrastructure\DAO\ExclusivityAssortmentMetricsLoaderDAO:
        arguments:
            $pdoMerch: '@App\Infrastructure\Connection\MerchConnectionAdapter'

    WF\Curation\ExclusivityAssortment\Infrastructure\Services\AssortmentToolCandidatesDbLoader:
        arguments:
            $pdo_merch: '@App\Infrastructure\Connection\MerchConnectionAdapter'

    WF\Curation\ExclusivityAssortment\Infrastructure\Services\AssortmentToolCandidatesWithPricesFromApiLoader:
        arguments:
            $pdo_merch: '@App\Infrastructure\Connection\MerchConnectionAdapter'

    WF\Curation\ExclusivityAssortment\Domain\Price\DAO\AssortmentPriceTiersLoaderDAO:
        arguments:
            $pdo_merch: '@App\Infrastructure\Connection\MerchConnectionAdapter'

    WF\Curation\ExclusivityAssortment\Domain\Interfaces\AssortmentToolProductSalePriceLoaderInterface: '@WF\Curation\ExclusivityAssortment\Infrastructure\Services\AssortmentToolProductSalePriceFromApiLoader'

    WF\Curation\ExclusivityAssortment\Domain\Price\Services\PriceLoader:
        arguments:
            $aletheiaApi: '@WF\Curation\ExclusivityAssortment\Domain\Price\Client\AletheiaApiClient'

    WF\Curation\ExclusivityAssortment\Infrastructure\Services\AssortmentToolProductSalePriceFromApiLoader:
        arguments:
            $pdo_merch: '@App\Infrastructure\Connection\MerchConnectionAdapter'
    # TODO temp block - end

    ### Assortment Library end ###

    # Production Tracking API
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Production_Tracking_Api_Logger_DAO: ~
    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Interfaces\Production_Tracking_Api_Logger_Storage: '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Storage\Production_Tracking_Api_Logger_DAO'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Production_Tracking_Api_Logger:
        arguments:
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Interfaces\Production_Tracking_Api_Logger_Storage'
            - '@Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface'
            - 'curation-tool'

    WF\Curation\ProductionTrackingApi\Client:
        arguments:
            - '@WF\Curation\ProductionTrackingApi\ClientConfig'

    WF\Curation\ProductionTrackingApi\ClientConfig:
        arguments:
            - '@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Production_Tracking_Api_Logger'
            - '%production_tracking_retry_count%'
            - '%production_tracking_connection_timeout%'
            - '%production-tracking-api-key%'
            - '%production-tracking-api-secret%'
            - '@=parameter("production_tracking_auth_url")[parameter("wf_env")]'
            - '@=parameter("production_tracking_api_url")[parameter("wf_env")]'

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Api\Production_Tracking_Api_Processor: ~

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Production_Tracking_Process_Factory: ~

    WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Interfaces\Production_Tracking_Processor:
        factory: ['@WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Curation_Production_Tracking\Production_Tracking_Process_Factory', 'get_processor']

    # WhiteLabel API Client
    WF\Curation\WhiteLabelApi\ClientConfig:
        arguments:
            - '@logger'
            - '%whitelabel_retry_count%'
            - '%whitelabel_connection_timeout%'
            - '%wf_env%'
            - '@GuzzleHttp\Client'

    WF\Curation\WhiteLabelApi\Client:
        arguments:
            - '@WF\Curation\WhiteLabelApi\ClientConfig'

    # Curation Graph API Client
    App\Infrastructure\Connection\Graphql\GraphClient:
        arguments:
            - '%wf_env%'
            - '@GuzzleHttp\Client'
            - '%whitelabel_connection_timeout%'

    # Curation Product GraphQL Service
    App\Infrastructure\Connection\Graphql\CurationProductGraphService:
        arguments:
            - '@App\Infrastructure\Connection\Graphql\GraphClient'

    # PuREST API Client

    WF\BrandWorkflows\PuREST\Infrastructure\UrlBuilder:
        arguments:
            - '@=parameter("all_services_lb")[parameter("datacenter")]'

    WF\BrandWorkflows\PuREST\Contract\UrlBuilderInterface: '@WF\BrandWorkflows\PuREST\Infrastructure\UrlBuilder'

    WF\BrandWorkflows\PuREST\Infrastructure\Client: ~
    WF\BrandWorkflows\PuREST\Contract\ClientInterface: '@WF\BrandWorkflows\PuREST\Infrastructure\Client'

    WF\BrandWorkflows\PuREST\Infrastructure\RequestFactory: ~
    WF\BrandWorkflows\PuREST\Contract\RequestFactoryInterface: '@WF\BrandWorkflows\PuREST\Infrastructure\RequestFactory'

    # Email

    WF\Shared\Helpers\Email_Helper:
        arguments:
            - '@WF\BrandWorkflows\PuREST\Contract\ClientInterface'
            - '@WF\BrandWorkflows\PuREST\Contract\RequestFactoryInterface'

    WF\Shared\Classes\ProductManagement\Mailer\Email_Helper_Adapter:
        arguments:
            - '@WF\Shared\Helpers\Email_Helper'

    WF\Shared\Classes\ProductManagement\Mailer\Mailer_Interface: '@WF\Shared\Classes\ProductManagement\Mailer\Email_Helper_Adapter'

    # WL NextGet API Client

    WF\Shared\Merchandising\Product_Transformation\:
        resource: '../libs/Shared/Merchandising/Product_Transformation'
        exclude: ['../libs/Shared/Merchandising/Product_Transformation/White_Label_Nextgen_Client/Model']

    wl_nextgen_api_cache_adapter:
        class: 'Symfony\Component\Cache\Adapter\PhpFilesAdapter'
        arguments:
            - '%app_name%.wl_nextgen_api' # default namespace
            - '%wl_nextgen_api_cache_ttl%'

    WF\Shared\Merchandising\Product_Transformation\White_Label_Nextgen_Client\Client\White_Label_Nextgen_API_Client:
        bind:
            $environment: '%wf_env%'

    WF\Shared\Curation\Api\Curation\TalentApi:
        arguments:
            - '%wf_env%'
            - '%talent-api-client-id%'
            - '%talent-api-client-secret%'

    WF\Shared\Merchandising\Product_Transformation\Client_Authenticator_Service:
        arguments:
            - '@logger'
            - '@wl_nextgen_api_cache_adapter'
            - '@GuzzleHttp\Client'
            - '%wl-nextgen-client-id%'
            - '%wl-nextgen-client-secret%'
            - '@=parameter("wl_nextgen_api_auth_url")[parameter("wf_env")]'

    WF\Shared\Merchandising\SKU_Collisions\Storage\SKU_Collider_DAO: ~

