<?php
/**
 * PHP version 8.1
 *
 * Updated Curation Eligibility Requirement with simplified conditions
 * 
 * New eligibility conditions:
 * 1. IsCoreClass (is_master_core_class)
 * 2. IsNotManufacturerHoldOut (!is_holdout_manufacturer)
 * 3. IsNotPerigoldOnly (!is_perigold_only)
 * 4. HasImages (has_images - For DS style predictions)
 *
 * <AUTHOR> for eligibility conditions update
 * @copyright 2024 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements;

use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model;

class Updated_Curation_Eligibility_Requirement implements SKU_Eligibility_Requirement_Interface {

  /**
   * Check if SKU is eligible for curation based on updated conditions
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku Request SKU
   *
   * @return bool
   */
  public function is_eligible(Curation_Request_SKU_Base_Model $request_sku) : bool {
    return $this->is_core_class($request_sku)
        && $this->is_not_manufacturer_holdout($request_sku)
        && $this->is_not_perigold_only($request_sku)
        && $this->has_images($request_sku);
  }

  /**
   * Check if SKU is core class
   * Condition 1: IsCoreClass
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku Request SKU
   * @return bool
   */
  private function is_core_class(Curation_Request_SKU_Base_Model $request_sku) : bool {
    return $request_sku->is_master_core_class() === true;
  }

  /**
   * Check if SKU is not manufacturer holdout
   * Condition 2: IsNotManufacturerHoldOut
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku Request SKU
   * @return bool
   */
  private function is_not_manufacturer_holdout(Curation_Request_SKU_Base_Model $request_sku) : bool {
    return !$request_sku->is_holdout_manufacturer();
  }

  /**
   * Check if SKU is not Perigold only
   * Condition 3: IsNotPerigoldOnly
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku Request SKU
   * @return bool
   */
  private function is_not_perigold_only(Curation_Request_SKU_Base_Model $request_sku) : bool {
    return !$request_sku->is_perigold_only();
  }

  /**
   * Check if SKU has images (For DS style predictions)
   * Condition 4: HasImages
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku Request SKU
   * @return bool
   */
  private function has_images(Curation_Request_SKU_Base_Model $request_sku) : bool {
    return $request_sku->has_images() === true;
  }

  /**
   * Get detailed eligibility breakdown for debugging
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku Request SKU
   * @return array
   */
  public function get_eligibility_breakdown(Curation_Request_SKU_Base_Model $request_sku) : array {
    return [
      'sku' => $request_sku->get_sku(),
      'conditions' => [
        'is_core_class' => [
          'result' => $this->is_core_class($request_sku),
          'description' => 'IsCoreClass - SKU must be master core class',
          'value' => $request_sku->is_master_core_class()
        ],
        'is_not_manufacturer_holdout' => [
          'result' => $this->is_not_manufacturer_holdout($request_sku),
          'description' => 'IsNotManufacturerHoldOut - SKU must not be manufacturer holdout',
          'value' => $request_sku->is_holdout_manufacturer()
        ],
        'is_not_perigold_only' => [
          'result' => $this->is_not_perigold_only($request_sku),
          'description' => 'IsNotPerigoldOnly - SKU must not be Perigold only',
          'value' => $request_sku->is_perigold_only()
        ],
        'has_images' => [
          'result' => $this->has_images($request_sku),
          'description' => 'HasImages - SKU must have images (For DS style predictions)',
          'value' => $request_sku->has_images()
        ]
      ],
      'overall_eligible' => $this->is_eligible($request_sku),
      'timestamp' => date('Y-m-d H:i:s')
    ];
  }

  /**
   * Get excluded reasons for ineligible SKUs
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku Request SKU
   * @return string
   */
  public function get_excluded_reasons(Curation_Request_SKU_Base_Model $request_sku) : string {
    $reasons = [];

    if (!$this->is_core_class($request_sku)) {
      $reasons[] = "Is not a core class";
    }

    if (!$this->is_not_manufacturer_holdout($request_sku)) {
      $reasons[] = "Is manufacturer holdout";
    }

    if (!$this->is_not_perigold_only($request_sku)) {
      $reasons[] = "Is Perigold only";
    }

    if (!$this->has_images($request_sku)) {
      $reasons[] = "Has no images";
    }

    return empty($reasons) ? "Eligible" : implode("; ", $reasons);
  }
}
