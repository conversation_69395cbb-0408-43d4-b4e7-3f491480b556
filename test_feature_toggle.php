<?php

/**
 * Standalone test script for GraphQL feature toggle
 * Run this directly: php test_feature_toggle.php
 */

require_once 'vendor/autoload.php';

use GuzzleHttp\Client as Guzzle;
use GuzzleHttp\Exception\GuzzleException;

class FeatureToggleTest
{
    private const DEV_URL = 'https://kube-general-feature-toggles.service.intradsm1.consul.csnzoo.com/api/client/features/curation_data_graphql_decoupling';
    private const DEV_AUTH = 'btbb-bc-curation-style-suggestion-api:development.0eb774f130525d62f36d77f84ec41a5f19bda020fbeae47f36133deb';

    private Guzzle $guzzle;

    public function __construct()
    {
        $this->guzzle = new Guzzle(['verify' => false]);
    }

    public function testFeatureToggle(): array
    {
        echo "=== Testing GraphQL Feature Toggle ===\n";
        
        $results = [
            'unleash_direct_test' => $this->testUnleashDirect(),
            'feature_toggle_exists' => $this->checkFeatureToggleExists(),
            'environment_check' => $this->checkEnvironment()
        ];

        return $results;
    }

    private function testUnleashDirect(): array
    {
        echo "1. Testing direct Unleash API call...\n";
        
        try {
            $response = $this->guzzle->request('GET', self::DEV_URL, [
                'headers' => [
                    'Authorization' => self::DEV_AUTH,
                    'Content-Type' => 'application/json'
                ],
                'timeout' => 30
            ]);

            $contents = $response->getBody()->getContents();
            $data = json_decode($contents, true);

            $result = [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'response_data' => $data,
                'is_enabled' => $data['enabled'] ?? false
            ];

            if (isset($data['strategies']) && !empty($data['strategies'])) {
                $strategy = $data['strategies'][0];
                $result['rollout_percentage'] = (int)($strategy['parameters']['rollout'] ?? 0);
            }

            echo "   ✅ Unleash API call successful\n";
            echo "   📊 Feature enabled: " . ($result['is_enabled'] ? 'YES' : 'NO') . "\n";
            echo "   📈 Rollout: " . ($result['rollout_percentage'] ?? 0) . "%\n";

            return $result;

        } catch (GuzzleException $e) {
            $result = [
                'success' => false,
                'error' => $e->getMessage(),
                'status_code' => $e->getCode()
            ];

            echo "   ❌ Unleash API call failed: " . $e->getMessage() . "\n";
            return $result;
        }
    }

    private function checkFeatureToggleExists(): array
    {
        echo "2. Checking if feature toggle exists in Unleash...\n";
        
        try {
            // Try to get all features for the project
            $projectUrl = 'https://kube-general-feature-toggles.service.intradsm1.consul.csnzoo.com/api/admin/projects/btbb-bc-curation-style-suggestion-api/features';
            
            $response = $this->guzzle->request('GET', $projectUrl, [
                'headers' => [
                    'Authorization' => self::DEV_AUTH,
                    'Content-Type' => 'application/json'
                ],
                'timeout' => 30
            ]);

            $contents = $response->getBody()->getContents();
            $data = json_decode($contents, true);

            $features = $data['features'] ?? [];
            $featureExists = false;
            
            foreach ($features as $feature) {
                if ($feature['name'] === 'curation_data_graphql_decoupling') {
                    $featureExists = true;
                    break;
                }
            }

            $result = [
                'success' => true,
                'feature_exists' => $featureExists,
                'total_features' => count($features),
                'project' => 'btbb-bc-curation-style-suggestion-api'
            ];

            echo "   📋 Feature exists: " . ($featureExists ? 'YES' : 'NO') . "\n";
            echo "   📊 Total features in project: " . count($features) . "\n";

            return $result;

        } catch (GuzzleException $e) {
            $result = [
                'success' => false,
                'error' => $e->getMessage()
            ];

            echo "   ❌ Failed to check feature existence: " . $e->getMessage() . "\n";
            return $result;
        }
    }

    private function checkEnvironment(): array
    {
        echo "3. Checking environment configuration...\n";
        
        $result = [
            'php_version' => PHP_VERSION,
            'guzzle_available' => class_exists('GuzzleHttp\Client'),
            'curl_available' => function_exists('curl_init'),
            'json_available' => function_exists('json_decode'),
            'current_directory' => getcwd(),
            'autoload_exists' => file_exists('vendor/autoload.php')
        ];

        echo "   🐘 PHP Version: " . $result['php_version'] . "\n";
        echo "   📦 Guzzle available: " . ($result['guzzle_available'] ? 'YES' : 'NO') . "\n";
        echo "   🌐 cURL available: " . ($result['curl_available'] ? 'YES' : 'NO') . "\n";

        return $result;
    }
}

// Run the test
try {
    $test = new FeatureToggleTest();
    $results = $test->testFeatureToggle();
    
    echo "\n=== Test Results ===\n";
    echo json_encode($results, JSON_PRETTY_PRINT) . "\n";
    
} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
