<?php

declare(strict_types=1);

/**
 * Test controller for GraphQL decoupling feature toggle
 * 
 * <AUTHOR> for MTBW-4912
 * @copyright 2024 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use App\Infrastructure\Connection\UnleashFeatureToggle\CurationGraphQLDecouplingToggle;
use App\Infrastructure\Connection\Graphql\CurationProductGraphService;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Data_Consistency_Validation_DAO;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use Exception;

class CurationGraphQLTestController extends AbstractController
{
    private CurationGraphQLDecouplingToggle $unleashToggle;
    private CurationProductGraphService $graphService;
    private Curation_Data_Consistency_Validation_DAO $dao;

    public function __construct(
        CurationGraphQLDecouplingToggle $unleashToggle,
        CurationProductGraphService $graphService,
        Curation_Data_Consistency_Validation_DAO $dao
    ) {
        $this->unleashToggle = $unleashToggle;
        $this->graphService = $graphService;
        $this->dao = $dao;
    }

    /**
     * Test feature toggle status
     *
     * @Route("/api/curation/graphql-test/toggle-status", name="curation_graphql_test_toggle_status", methods={"GET"})
     */
    public function getToggleStatus(): JsonResponse
    {
        try {
            $isEnabled = $this->unleashToggle->isGraphQLDecouplingEnabled();
            $rolloutPercentage = $this->unleashToggle->getRolloutPercentage();

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'feature_toggle' => 'curation_data_graphql_decoupling',
                    'is_enabled' => $isEnabled,
                    'rollout_percentage' => $rolloutPercentage,
                    'timestamp' => date('Y-m-d H:i:s'),
                    'environment' => $_ENV['WF_ENV'] ?? 'unknown'
                ]
            ]);
        } catch (Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => [
                    'message' => 'Failed to check feature toggle status',
                    'details' => $e->getMessage()
                ]
            ], 500);
        }
    }

    /**
     * Test SKUs missing class functionality
     *
     * @Route("/api/curation/graphql-test/test-skus-missing-class", name="curation_graphql_test_skus_missing_class", methods={"POST"})
     */
    public function testSkusMissingClass(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $skus = $data['skus'] ?? [];

            if (empty($skus)) {
                return new JsonResponse([
                    'success' => false,
                    'error' => [
                        'message' => 'SKUs array is required',
                        'example' => ['skus' => ['SKU001', 'SKU002']]
                    ]
                ], 400);
            }

            $isToggleEnabled = $this->unleashToggle->isGraphQLDecouplingEnabled();
            $startTime = microtime(true);
            
            $result = $this->dao->get_skus_missing_class($skus);
            
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2); // milliseconds

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'method' => 'get_skus_missing_class',
                    'input_skus' => $skus,
                    'missing_class_skus' => $result,
                    'feature_toggle_enabled' => $isToggleEnabled,
                    'implementation_used' => $isToggleEnabled ? 'GraphQL' : 'SQL',
                    'execution_time_ms' => $executionTime,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => [
                    'message' => 'Failed to test SKUs missing class',
                    'details' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            ], 500);
        }
    }

    /**
     * Test all methods with comparison
     *
     * @Route("/api/curation/graphql-test/test-all-methods", name="curation_graphql_test_all_methods", methods={"POST"})
     */
    public function testAllMethods(Request $request): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);
            $skus = $data['skus'] ?? [];

            if (empty($skus)) {
                return new JsonResponse([
                    'success' => false,
                    'error' => [
                        'message' => 'SKUs array is required',
                        'example' => ['skus' => ['SKU001', 'SKU002']]
                    ]
                ], 400);
            }

            $isToggleEnabled = $this->unleashToggle->isGraphQLDecouplingEnabled();
            $rolloutPercentage = $this->unleashToggle->getRolloutPercentage();

            $results = [];

            // Test each method
            $methods = [
                'get_skus_missing_class',
                'get_skus_invalid_brand_catalog',
                'get_skus_with_kit_parents_and_kit_children_data'
            ];

            foreach ($methods as $method) {
                $startTime = microtime(true);
                $result = $this->dao->$method($skus);
                $endTime = microtime(true);
                $executionTime = round(($endTime - $startTime) * 1000, 2);

                $results[$method] = [
                    'result' => $result,
                    'execution_time_ms' => $executionTime
                ];
            }

            return new JsonResponse([
                'success' => true,
                'data' => [
                    'input_skus' => $skus,
                    'feature_toggle' => [
                        'enabled' => $isToggleEnabled,
                        'rollout_percentage' => $rolloutPercentage,
                        'implementation_used' => $isToggleEnabled ? 'GraphQL' : 'SQL'
                    ],
                    'results' => $results,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => [
                    'message' => 'Failed to test all methods',
                    'details' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            ], 500);
        }
    }

    /**
     * Simple health check endpoint
     *
     * @Route("/api/curation/graphql-test/health", name="curation_graphql_test_health", methods={"GET"})
     */
    public function healthCheck(): JsonResponse
    {
        return new JsonResponse([
            'success' => true,
            'message' => 'GraphQL Test Controller is working',
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
