<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * Feature toggle for updated curation eligibility conditions
 * Controls rollout of new simplified eligibility requirements
 *
 * <AUTHOR> for eligibility conditions update
 * @copyright 2024 Wayfair LLC - All rights reserved
 */

namespace App\Infrastructure\Connection\UnleashFeatureToggle;

use Guz<PERSON>Http\Client as Guzzle;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Log\LoggerInterface;

class UpdatedCurationEligibilityToggle
{
    private const FEATURE_NAME = 'updated_curation_eligibility_conditions';
    
    // Unleash URLs for different environments
    private const UNLEASH_DEV_URL = 'https://kube-general-feature-toggles.service.intradsm1.consul.csnzoo.com/api/client/features/' . self::FEATURE_NAME;
    private const UNLEASH_PROD_URL = 'https://kube-general-feature-toggles.service.intradsm1.consul.csnzoo.com/api/client/features/' . self::FEATURE_NAME;

    // Auth tokens for the correct project
    // Format: project-name:environment.token
    public const UPDATED_ELIGIBILITY_TOGGLE_PROD_AUTH = 'btbb-bc-curation-style-suggestion-api:production.a0db50fcaa19af7a63878e924e9747f7e18f757fa1fa038bf492d68d';
    public const UPDATED_ELIGIBILITY_TOGGLE_DEV_AUTH = 'btbb-bc-curation-style-suggestion-api:development.0eb774f130525d62f36d77f84ec41a5f19bda020fbeae47f36133deb';

    private Guzzle $guzzle;
    private LoggerInterface $logger;
    private string $environment;

    public function __construct(LoggerInterface $logger)
    {
        $this->guzzle = new Guzzle(['verify' => false]);
        $this->logger = $logger;
        $this->environment = $_ENV['WF_ENV'] ?? 'dev';
    }

    /**
     * Check if updated curation eligibility conditions are enabled
     *
     * @return bool
     */
    public function isUpdatedEligibilityEnabled(): bool
    {
        try {
            $response = $this->makeUnleashRequest();
            
            if (!$response) {
                $this->logger->warning('Updated eligibility toggle: Failed to get response from Unleash, defaulting to false');
                return false;
            }

            $isEnabled = $response['enabled'] ?? false;
            $rolloutPercentage = $this->extractRolloutPercentage($response);

            $this->logger->info('Updated eligibility toggle status', [
                'feature_name' => self::FEATURE_NAME,
                'enabled' => $isEnabled,
                'rollout_percentage' => $rolloutPercentage,
                'environment' => $this->environment
            ]);

            return $isEnabled && $this->isUserInRollout($rolloutPercentage);

        } catch (\Exception $e) {
            $this->logger->error('Updated eligibility toggle: Exception occurred', [
                'error' => $e->getMessage(),
                'feature_name' => self::FEATURE_NAME
            ]);
            return false;
        }
    }

    /**
     * Get the current rollout percentage
     *
     * @return int
     */
    public function getRolloutPercentage(): int
    {
        try {
            $response = $this->makeUnleashRequest();
            return $this->extractRolloutPercentage($response);
        } catch (\Exception $e) {
            $this->logger->error('Updated eligibility toggle: Failed to get rollout percentage', [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * Make request to Unleash API
     *
     * @return array|null
     */
    private function makeUnleashRequest(): ?array
    {
        $url = $this->getUnleashUrl();
        $authToken = $this->getAuthToken();

        try {
            $response = $this->guzzle->request('GET', $url, [
                'headers' => [
                    'Authorization' => $authToken,
                    'Content-Type' => 'application/json'
                ],
                'timeout' => 5
            ]);

            $contents = $response->getBody()->getContents();
            return json_decode($contents, true);

        } catch (GuzzleException $e) {
            $this->logger->error('Updated eligibility toggle: Unleash API request failed', [
                'url' => $url,
                'error' => $e->getMessage(),
                'status_code' => $e->getCode()
            ]);
            return null;
        }
    }

    /**
     * Extract rollout percentage from Unleash response
     *
     * @param array|null $response
     * @return int
     */
    private function extractRolloutPercentage(?array $response): int
    {
        if (!$response || !isset($response['strategies'])) {
            return 0;
        }

        foreach ($response['strategies'] as $strategy) {
            if (isset($strategy['parameters']['rollout'])) {
                return (int)$strategy['parameters']['rollout'];
            }
        }

        return 0;
    }

    /**
     * Determine if current user/request is in the rollout percentage
     *
     * @param int $rolloutPercentage
     * @return bool
     */
    private function isUserInRollout(int $rolloutPercentage): bool
    {
        if ($rolloutPercentage >= 100) {
            return true;
        }

        if ($rolloutPercentage <= 0) {
            return false;
        }

        // Simple hash-based rollout determination
        // In production, you might want to use user ID or session ID
        $identifier = $_SERVER['REQUEST_URI'] ?? 'default';
        $hash = crc32($identifier) % 100;
        
        return $hash < $rolloutPercentage;
    }

    /**
     * Get Unleash URL based on environment
     *
     * @return string
     */
    private function getUnleashUrl(): string
    {
        return $this->environment === 'production' ? self::UNLEASH_PROD_URL : self::UNLEASH_DEV_URL;
    }

    /**
     * Get auth token based on environment
     *
     * @return string
     */
    private function getAuthToken(): string
    {
        return $this->environment === 'production' 
            ? self::UPDATED_ELIGIBILITY_TOGGLE_PROD_AUTH 
            : self::UPDATED_ELIGIBILITY_TOGGLE_DEV_AUTH;
    }

    /**
     * Get feature toggle status for debugging
     *
     * @return array
     */
    public function getToggleStatus(): array
    {
        $response = $this->makeUnleashRequest();
        
        return [
            'feature_name' => self::FEATURE_NAME,
            'environment' => $this->environment,
            'unleash_response' => $response,
            'is_enabled' => $this->isUpdatedEligibilityEnabled(),
            'rollout_percentage' => $this->getRolloutPercentage(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}
