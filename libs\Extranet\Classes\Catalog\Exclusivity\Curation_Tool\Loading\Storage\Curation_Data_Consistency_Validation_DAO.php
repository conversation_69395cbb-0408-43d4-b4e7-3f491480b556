<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\Helper\SQLBulkHelper;
use App\Infrastructure\Connection\Graphql\CurationProductGraphService;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use App\Domain\Model\FeatureToggle;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validation_Result;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validation_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validator_Storage;
use WF\Shared\Helpers\SQL;
use PDO;

class Curation_Data_Consistency_Validation_DAO implements Curation_Data_Consistency_Validation_Storage, Curation_Data_Consistency_Validator_Storage {

  private ProductConnection $pdo;
  private CurationProductGraphService $graphService;
  private FeatureTogglesInterface $featureToggles;

  /**
   * @param ProductConnection $pdo PDO
   * @param CurationProductGraphService $graphService GraphQL service for product queries
   * @param FeatureTogglesInterface $featureToggles Feature toggle service
   */
  public function __construct(
    ProductConnection $pdo,
    CurationProductGraphService $graphService,
    FeatureTogglesInterface $featureToggles
  ) {
    $this->pdo = $pdo;
    $this->graphService = $graphService;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return string[]
   */
  public function get_saved_kit_parents(int $batch_id) : array {
    $sql = 'SELECT ViSKU AS SKU
            FROM csn_product.dbo.tblVerificationItem WITH (NOLOCK)
            WHERE ViBatchID = :batch_id
            AND EXISTS (SELECT TOP 1 1 FROM csn_product.dbo.vwExclusivityKitCompositionActive WITH (NOLOCK) WHERE ParentSKU = ViSKU)
            ORDER BY ViSKU';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);


    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get saved kit parents');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param string[] $skus SKUS
   *
   * @return string[]
   */
  public function get_skus_missing_class(array $skus) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING)) {
      return $this->get_skus_missing_class_graphql($skus);
    } else {
      return $this->get_skus_missing_class_sql($skus);
    }
  }

  /**
   * Get SKUs missing class via GraphQL
   *
   * @param string[] $skus SKUS
   * @return string[]
   */
  private function get_skus_missing_class_graphql(array $skus) : array {
    try {
      return $this->graphService->getSkusMissingClass($skus);
    } catch (\Exception $e) {
      // Log error and fallback to SQL if GraphQL fails
      error_log("GraphQL query failed for get_skus_missing_class, falling back to SQL: " . $e->getMessage());
      return $this->get_skus_missing_class_sql($skus);
    }
  }

  /**
   * Get SKUs missing class via SQL (original implementation)
   *
   * @param string[] $skus SKUS
   * @return string[]
   */
  private function get_skus_missing_class_sql(array $skus) : array {
    $sql = 'DECLARE @SKUS TABLE (SKU NVARCHAR(8))

      INSERT INTO @SKUS (SKU)
      SELECT PrSKU FROM csn_product.dbo.tblProduct WITH (NOLOCK)
      WHERE PrSKU IN ' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . '

      SELECT SKU AS SKU
      FROM @SKUS skus
      WHERE NOT EXISTS (
            SELECT TOP 1 1
            FROM csn_product.dbo.tbljoinProductClass pc WITH (NOLOCK)
            WHERE pc.PrSKU = skus.SKU AND pc.PcMasterClass = 1
      )
      ORDER BY skus.sku
      ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get skus missing class');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param string[] $skus SKUS
   *
   * @return string[]
   */
  public function get_skus_invalid_brand_catalog(array $skus) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING)) {
      return $this->get_skus_invalid_brand_catalog_graphql($skus);
    } else {
      return $this->get_skus_invalid_brand_catalog_sql($skus);
    }
  }

  /**
   * Get SKUs with invalid brand catalog via GraphQL
   *
   * @param string[] $skus SKUS
   * @return string[]
   */
  private function get_skus_invalid_brand_catalog_graphql(array $skus) : array {
    try {
      return $this->graphService->getSkusInvalidBrandCatalog($skus);
    } catch (\Exception $e) {
      // Log error and fallback to SQL if GraphQL fails
      error_log("GraphQL query failed for get_skus_invalid_brand_catalog, falling back to SQL: " . $e->getMessage());
      return $this->get_skus_invalid_brand_catalog_sql($skus);
    }
  }

  /**
   * Get SKUs with invalid brand catalog via SQL (original implementation)
   *
   * @param string[] $skus SKUS
   * @return string[]
   */
  private function get_skus_invalid_brand_catalog_sql(array $skus) : array {
    $sql = '
      SELECT PrSKU AS SKU
      FROM csn_product.dbo.tblProduct WITH (NOLOCK)
      WHERE PrSKU IN ' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ' AND PrBclgId = 0
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get SKUs with invalid Brand Catalog');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

    /**
     * @param string[] $skus SKUs
     *
     * @return array<string[]>
     */
  public function get_skus_with_kit_parents_and_kit_children_data(array $skus) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING)) {
      return $this->get_skus_with_kit_parents_and_kit_children_data_graphql($skus);
    } else {
      return $this->get_skus_with_kit_parents_and_kit_children_data_sql($skus);
    }
  }

  /**
   * Get SKUs with kit parents and children data via GraphQL
   *
   * @param string[] $skus SKUs
   * @return array<string[]>
   */
  private function get_skus_with_kit_parents_and_kit_children_data_graphql(array $skus) : array {
    try {
      return $this->graphService->getSkusWithKitData($skus);
    } catch (\Exception $e) {
      // Log error and fallback to SQL if GraphQL fails
      error_log("GraphQL query failed for get_skus_with_kit_parents_and_kit_children_data, falling back to SQL: " . $e->getMessage());
      return $this->get_skus_with_kit_parents_and_kit_children_data_sql($skus);
    }
  }

  /**
   * Get SKUs with kit parents and children data via SQL (original implementation)
   *
   * @param string[] $skus SKUs
   * @return array<string[]>
   */
  private function get_skus_with_kit_parents_and_kit_children_data_sql(array $skus) : array {
    $sql = 'DECLARE @SKUS TABLE (SKU NVARCHAR(8))

      INSERT INTO @SKUS (SKU)
      SELECT PrSKU FROM csn_product.dbo.tblProduct WITH (NOLOCK)
      WHERE PrSKU IN ' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . '

      SELECT skus.SKU AS sku, parent.SKU as parent_sku, child.SKU as child_sku
      FROM @SKUS skus
      CROSS APPLY (
	       SELECT TOP 1 kit.ChildSKU AS SKU FROM csn_product.dbo.vwExclusivityKitCompositionActive  kit WITH (NOLOCK) WHERE kit.ParentSku = skus.sku
      ) parent
      CROSS APPLY (
	       SELECT TOP 1 kit.ParentSKU AS SKU FROM csn_product.dbo.vwExclusivityKitCompositionActive kit WITH (NOLOCK) WHERE kit.ChildSKU = skus.sku
      ) child
      ORDER BY skus.sku
      ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get skus with kit parents and kit children');
    }

    return $statement->fetchAll();
  }

  /**
   * @param string[] $parents Parents
   *
   * @return array
   */
  public function get_kit_parents_with_children(array $parents) : array {
    $sql = '
        SELECT 
          kit.ParentSku AS parent_sku,
          kit.ChildSKU AS child_sku
        FROM csn_product.dbo.vwExclusivityKitCompositionActive  kit WITH (NOLOCK) 
	      WHERE kit.ParentSku IN ' . $this->pdo->paramsForList(count($parents), 'parent', SQL::nvarchar(8)) . '
        ORDER BY kit.ParentSku
      ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('parent', $parents, SQL::nvarchar(8));


    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get kit children');
    }

    return $statement->fetchAll(PDO::FETCH_GROUP | PDO::FETCH_COLUMN);
  }

  /**
   * @param int                                           $batch_id Batch ID
   * @param Curation_Data_Consistency_Validation_Result[] $results  Validation Results
   *
   * @return void
   */
  public function save_results(int $batch_id, array $results) {
    $columns = [
        'BatchID' => SQL::int,
        'SKU'     => SQL::nvarchar(8),
        'Error'   => SQL::nvarchar(100)
    ];

    $items = [];

    foreach ($results as $result) {
      foreach ($result->get_error_sku_map() as $sku => $error) {
        $items[] = [
            'BatchID' => $batch_id,
            'SKU'     => $sku,
            'Error'   => $error
        ];
      }
    }

    $sql = SQLBulkHelper::getTempTableJsonSql($columns, 'tmpVerificationItemDataConsistencyLog') . PHP_EOL;
    $sql .= 'INSERT INTO csn_product.dbo.tblVerificationItemDataConsistencyLog (BatchID, SKU, Error)
            SELECT  BatchID, SKU, Error FROM #tmpVerificationItemDataConsistencyLog';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(SQLBulkHelper::DEFAULT_JSON_PARAM, json_encode($items), PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot save validation results');
    }
  }
}