<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Wayfair LLC - All rights reserved
 */

namespace WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage;

use App\Domain\Model\FeatureToggle;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use App\Infrastructure\Helper\SQLBulkHelper;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validation_Result;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validation_Storage;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Data_Consistency\Curation_Data_Consistency_Validator_Storage;
use WF\Shared\Helpers\SQL;
use PDO;

class Curation_Data_Consistency_Valida<PERSON>_<PERSON> implements Curation_Data_Consistency_Validation_Storage, Curation_Data_Consistency_Validator_Storage {

  private ProductConnection $pdo;
  private ProductConnection $pdo_psql;
  private FeatureTogglesInterface $featureToggles;

  /**
   * @param ProductConnection $pdo PDO
   * @param ProductConnection $pdo_psql PostgreSQL PDO
   * @param FeatureTogglesInterface $featureToggles Feature Toggles
   */
  public function __construct(ProductConnection $pdo, ProductConnection $pdo_psql, FeatureTogglesInterface $featureToggles) {
    $this->pdo = $pdo;
    $this->pdo_psql = $pdo_psql;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return string[]
   */
  public function get_saved_kit_parents(int $batch_id) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      return $this->get_saved_kit_parents_psql($batch_id);
    } else {
      return $this->get_saved_kit_parents_sql($batch_id);
    }
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return string[]
   */
  private function get_saved_kit_parents_sql(int $batch_id) : array {
    $sql = 'SELECT ViSKU AS SKU
            FROM csn_product.dbo.tblVerificationItem WITH (NOLOCK)
            WHERE ViBatchID = :batch_id
            AND EXISTS (SELECT TOP 1 1 FROM csn_product.dbo.vwExclusivityKitCompositionActive WITH (NOLOCK) WHERE ParentSKU = ViSKU)
            ORDER BY ViSKU';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get saved kit parents');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param int $batch_id Batch ID
   *
   * @return string[]
   */
  private function get_saved_kit_parents_psql(int $batch_id) : array {
    $sql = 'SELECT vi."ViSKU" AS SKU
            FROM "tblVerificationItem" vi
            WHERE vi."ViBatchID" = :batch_id
            AND EXISTS (SELECT 1 FROM "vwExclusivityKitCompositionActive" kit WHERE kit."ParentSKU" = vi."ViSKU")
            ORDER BY vi."ViSKU"';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':batch_id', $batch_id, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get saved kit parents from PostgreSQL');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param string[] $skus SKUS
   *
   * @return string[]
   */
  public function get_skus_missing_class(array $skus) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      return $this->get_skus_missing_class_psql($skus);
    } else {
      return $this->get_skus_missing_class_sql($skus);
    }
  }

  /**
   * @param string[] $skus SKUS
   *
   * @return string[]
   */
  private function get_skus_missing_class_sql(array $skus) : array {
    $sql = 'DECLARE @SKUS TABLE (SKU NVARCHAR(8))

      INSERT INTO @SKUS (SKU)
      SELECT PrSKU FROM csn_product.dbo.tblProduct WITH (NOLOCK)
      WHERE PrSKU IN ' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . '

      SELECT SKU AS SKU
      FROM @SKUS skus
      WHERE NOT EXISTS (
            SELECT TOP 1 1
            FROM csn_product.dbo.tbljoinProductClass pc WITH (NOLOCK)
            WHERE pc.PrSKU = skus.SKU AND pc.PcMasterClass = 1
      )
      ORDER BY skus.sku
      ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get skus missing class');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param string[] $skus SKUS
   *
   * @return string[]
   */
  private function get_skus_missing_class_psql(array $skus) : array {
    $sql = '
      SELECT p."PrSKU" AS SKU
      FROM "tblProduct" p
      WHERE p."PrSKU" IN ' . $this->pdo_psql->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . '
        AND NOT EXISTS (
            SELECT 1
            FROM "tbljoinProductClass" pc
            WHERE pc."PrSKU" = p."PrSKU" AND pc."PcMasterClass" = 1
        )
      ORDER BY p."PrSKU"
      ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get skus missing class from PostgreSQL');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param string[] $skus SKUS
   *
   * @return string[]
   */
  public function get_skus_invalid_brand_catalog(array $skus) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      return $this->get_skus_invalid_brand_catalog_psql($skus);
    } else {
      return $this->get_skus_invalid_brand_catalog_sql($skus);
    }
  }

  /**
   * @param string[] $skus SKUS
   *
   * @return string[]
   */
  private function get_skus_invalid_brand_catalog_sql(array $skus) : array {
    $sql = '
      SELECT PrSKU AS SKU
      FROM csn_product.dbo.tblProduct WITH (NOLOCK)
      WHERE PrSKU IN ' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ' AND PrBclgId = 0
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get SKUs with invalid Brand Catalog');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param string[] $skus SKUS
   *
   * @return string[]
   */
  private function get_skus_invalid_brand_catalog_psql(array $skus) : array {
    $sql = '
      SELECT "PrSKU" AS SKU
      FROM "tblProduct"
      WHERE "PrSKU" IN ' . $this->pdo_psql->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . ' AND "PrBclgId" = 0
    ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get SKUs with invalid Brand Catalog from PostgreSQL');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

    /**
     * @param string[] $skus SKUs
     *
     * @return array<string[]>
     */
  public function get_skus_with_kit_parents_and_kit_children_data(array $skus) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      return $this->get_skus_with_kit_parents_and_kit_children_data_psql($skus);
    } else {
      return $this->get_skus_with_kit_parents_and_kit_children_data_sql($skus);
    }
  }

  /**
   * @param string[] $skus SKUs
   *
   * @return array<string[]>
   */
  private function get_skus_with_kit_parents_and_kit_children_data_sql(array $skus) : array {
    $sql = 'DECLARE @SKUS TABLE (SKU NVARCHAR(8))

      INSERT INTO @SKUS (SKU)
      SELECT PrSKU FROM csn_product.dbo.tblProduct WITH (NOLOCK)
      WHERE PrSKU IN ' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . '

      SELECT skus.SKU AS sku, parent.SKU as parent_sku, child.SKU as child_sku
      FROM @SKUS skus
      CROSS APPLY (
	       SELECT TOP 1 kit.ChildSKU AS SKU FROM csn_product.dbo.vwExclusivityKitCompositionActive  kit WITH (NOLOCK) WHERE kit.ParentSku = skus.sku
      ) parent
      CROSS APPLY (
	       SELECT TOP 1 kit.ParentSKU AS SKU FROM csn_product.dbo.vwExclusivityKitCompositionActive kit WITH (NOLOCK) WHERE kit.ChildSKU = skus.sku
      ) child
      ORDER BY skus.sku
      ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get skus with kit parents and kit children');
    }

    return $statement->fetchAll();
  }

  /**
   * @param string[] $skus SKUs
   *
   * @return array<string[]>
   */
  private function get_skus_with_kit_parents_and_kit_children_data_psql(array $skus) : array {
    $sql = '
      SELECT p."PrSKU" AS sku, parent.sku as parent_sku, child.sku as child_sku
      FROM "tblProduct" p
      CROSS JOIN LATERAL (
        SELECT kit."ChildSKU" AS sku FROM "vwExclusivityKitCompositionActive" kit WHERE kit."ParentSku" = p."PrSKU" LIMIT 1
      ) parent
      CROSS JOIN LATERAL (
        SELECT kit."ParentSKU" AS sku FROM "vwExclusivityKitCompositionActive" kit WHERE kit."ChildSKU" = p."PrSKU" LIMIT 1
      ) child
      WHERE p."PrSKU" IN ' . $this->pdo_psql->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . '
      ORDER BY p."PrSKU"
      ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get skus with kit parents and kit children from PostgreSQL');
    }

    return $statement->fetchAll();
  }

  /**
   * @param string[] $parents Parents
   *
   * @return array
   */
  public function get_kit_parents_with_children(array $parents) : array {
    $sql = '
        SELECT 
          kit.ParentSku AS parent_sku,
          kit.ChildSKU AS child_sku
        FROM csn_product.dbo.vwExclusivityKitCompositionActive  kit WITH (NOLOCK) 
	      WHERE kit.ParentSku IN ' . $this->pdo->paramsForList(count($parents), 'parent', SQL::nvarchar(8)) . '
        ORDER BY kit.ParentSku
      ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('parent', $parents, SQL::nvarchar(8));


    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get kit children');
    }

    return $statement->fetchAll(PDO::FETCH_GROUP | PDO::FETCH_COLUMN);
  }

  /**
   * @param int                                           $batch_id Batch ID
   * @param Curation_Data_Consistency_Validation_Result[] $results  Validation Results
   *
   * @return void
   */
  public function save_results(int $batch_id, array $results) {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $this->save_results_psql($batch_id, $results);
    } else {
      $this->save_results_sql($batch_id, $results);
    }
  }

  /**
   * @param int                                           $batch_id Batch ID
   * @param Curation_Data_Consistency_Validation_Result[] $results  Validation Results
   *
   * @return void
   */
  private function save_results_sql(int $batch_id, array $results) {
    $columns = [
        'BatchID' => SQL::int,
        'SKU'     => SQL::nvarchar(8),
        'Error'   => SQL::nvarchar(100)
    ];

    $items = [];

    foreach ($results as $result) {
      foreach ($result->get_error_sku_map() as $sku => $error) {
        $items[] = [
            'BatchID' => $batch_id,
            'SKU'     => $sku,
            'Error'   => $error
        ];
      }
    }

    $sql = SQLBulkHelper::getTempTableJsonSql($columns, 'tmpVerificationItemDataConsistencyLog') . PHP_EOL;
    $sql .= 'INSERT INTO csn_product.dbo.tblVerificationItemDataConsistencyLog (BatchID, SKU, Error)
            SELECT  BatchID, SKU, Error FROM #tmpVerificationItemDataConsistencyLog';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(SQLBulkHelper::DEFAULT_JSON_PARAM, json_encode($items), PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot save validation results');
    }
  }

  /**
   * @param int                                           $batch_id Batch ID
   * @param Curation_Data_Consistency_Validation_Result[] $results  Validation Results
   *
   * @return void
   */
  private function save_results_psql(int $batch_id, array $results) {
    $items = [];

    foreach ($results as $result) {
      foreach ($result->get_error_sku_map() as $sku => $error) {
        $items[] = [
            'BatchID' => $batch_id,
            'SKU'     => $sku,
            'Error'   => $error
        ];
      }
    }

    if (empty($items)) {
      return;
    }

    $sql = 'INSERT INTO "tblVerificationItemDataConsistencyLog" ("BatchID", "SKU", "Error") VALUES ';
    $values = [];
    $params = [];

    foreach ($items as $index => $item) {
      $values[] = "(:batch_id_{$index}, :sku_{$index}, :error_{$index})";
      $params["batch_id_{$index}"] = $item['BatchID'];
      $params["sku_{$index}"] = $item['SKU'];
      $params["error_{$index}"] = $item['Error'];
    }

    $sql .= implode(', ', $values);

    $statement = $this->pdo_psql->prepare($sql);

    foreach ($params as $param => $value) {
      $statement->bindValue(":{$param}", $value, is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR);
    }

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot save validation results to PostgreSQL');
    }
  }
}