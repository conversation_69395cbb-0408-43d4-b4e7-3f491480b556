<?php

declare(strict_types=1);

/**
 * Test for Curation_Data_Consistency_Validation_DAO with GraphQL decoupling
 * 
 * <AUTHOR> for MTBW-4912
 * @copyright 2024 Wayfair LLC - All rights reserved
 */

namespace App\Tests\Unit\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\Graphql\CurationProductGraphService;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use App\Domain\Model\FeatureToggle;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Data_Consistency_Validation_DAO;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

class Curation_Data_Consistency_Validation_DAO_Test extends TestCase
{
    private Curation_Data_Consistency_Validation_DAO $dao;
    private MockObject $mockPdo;
    private MockObject $mockGraphService;
    private MockObject $mockFeatureToggles;

    protected function setUp(): void
    {
        $this->mockPdo = $this->createMock(ProductConnection::class);
        $this->mockGraphService = $this->createMock(CurationProductGraphService::class);
        $this->mockFeatureToggles = $this->createMock(FeatureTogglesInterface::class);

        $this->dao = new Curation_Data_Consistency_Validation_DAO(
            $this->mockPdo,
            $this->mockGraphService,
            $this->mockFeatureToggles
        );
    }

    public function testGetSkusMissingClassWithGraphQLEnabled(): void
    {
        $skus = ['SKU001', 'SKU002'];
        $expectedResult = ['SKU002'];

        // Feature toggle enabled - should use GraphQL
        $this->mockFeatureToggles
            ->expects($this->once())
            ->method('isEnabled')
            ->with(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING)
            ->willReturn(true);

        $this->mockGraphService
            ->expects($this->once())
            ->method('getSkusMissingClass')
            ->with($skus)
            ->willReturn($expectedResult);

        // PDO should not be called when GraphQL is enabled
        $this->mockPdo
            ->expects($this->never())
            ->method('prepare');

        $result = $this->dao->get_skus_missing_class($skus);

        $this->assertEquals($expectedResult, $result);
    }

    public function testGetSkusMissingClassWithGraphQLDisabled(): void
    {
        $skus = ['SKU001', 'SKU002'];
        $expectedResult = ['SKU002'];

        // Feature toggle disabled - should use SQL
        $this->mockFeatureToggles
            ->expects($this->once())
            ->method('isEnabled')
            ->with(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING)
            ->willReturn(false);

        // GraphQL service should not be called when feature is disabled
        $this->mockGraphService
            ->expects($this->never())
            ->method('getSkusMissingClass');

        // Mock PDO behavior for SQL execution
        $mockStatement = $this->createMock(\PDOStatement::class);
        $mockStatement
            ->expects($this->once())
            ->method('execute')
            ->willReturn(true);
        $mockStatement
            ->expects($this->once())
            ->method('fetchAll')
            ->willReturn($expectedResult);

        $this->mockPdo
            ->expects($this->once())
            ->method('paramsForList')
            ->willReturn('(:sku0, :sku1)');
        $this->mockPdo
            ->expects($this->once())
            ->method('prepare')
            ->willReturn($mockStatement);

        $result = $this->dao->get_skus_missing_class($skus);

        $this->assertEquals($expectedResult, $result);
    }

    public function testGetSkusInvalidBrandCatalogWithGraphQLEnabled(): void
    {
        $skus = ['SKU001', 'SKU002'];
        $expectedResult = ['SKU002'];

        $this->mockFeatureToggles
            ->expects($this->once())
            ->method('isEnabled')
            ->with(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING)
            ->willReturn(true);

        $this->mockGraphService
            ->expects($this->once())
            ->method('getSkusInvalidBrandCatalog')
            ->with($skus)
            ->willReturn($expectedResult);

        $result = $this->dao->get_skus_invalid_brand_catalog($skus);

        $this->assertEquals($expectedResult, $result);
    }

    public function testGetSkusWithKitDataWithGraphQLEnabled(): void
    {
        $skus = ['SKU001', 'SKU002'];
        $expectedResult = [
            ['sku' => 'SKU001', 'parent_sku' => 'PARENT001', 'child_sku' => null]
        ];

        $this->mockFeatureToggles
            ->expects($this->once())
            ->method('isEnabled')
            ->with(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING)
            ->willReturn(true);

        $this->mockGraphService
            ->expects($this->once())
            ->method('getSkusWithKitData')
            ->with($skus)
            ->willReturn($expectedResult);

        $result = $this->dao->get_skus_with_kit_parents_and_kit_children_data($skus);

        $this->assertEquals($expectedResult, $result);
    }

    public function testGraphQLFallbackToSQL(): void
    {
        $skus = ['SKU001', 'SKU002'];
        $expectedResult = ['SKU002'];

        // Feature toggle enabled but GraphQL fails
        $this->mockFeatureToggles
            ->expects($this->once())
            ->method('isEnabled')
            ->with(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING)
            ->willReturn(true);

        // GraphQL service throws exception
        $this->mockGraphService
            ->expects($this->once())
            ->method('getSkusMissingClass')
            ->with($skus)
            ->willThrowException(new \Exception('GraphQL service unavailable'));

        // Should fallback to SQL
        $mockStatement = $this->createMock(\PDOStatement::class);
        $mockStatement
            ->expects($this->once())
            ->method('execute')
            ->willReturn(true);
        $mockStatement
            ->expects($this->once())
            ->method('fetchAll')
            ->willReturn($expectedResult);

        $this->mockPdo
            ->expects($this->once())
            ->method('paramsForList')
            ->willReturn('(:sku0, :sku1)');
        $this->mockPdo
            ->expects($this->once())
            ->method('prepare')
            ->willReturn($mockStatement);

        $result = $this->dao->get_skus_missing_class($skus);

        $this->assertEquals($expectedResult, $result);
    }
}
