<?php

declare(strict_types=1);

namespace App\Infrastructure\Connection\UnleashFeatureToggle;

use App\Application\Logger\LoggerTrait;
use GuzzleHttp\Client as Guzzle;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Log\LoggerAwareTrait;
use WF\Shared\Environment;
use function json_decode;

class CurationGraphQLDecouplingToggle
{
    use LoggerTrait;
    use LoggerAwareTrait;

    /**
     * @var string
     */
    protected string $url;

    /**
     * @var string
     */
    protected string $auth;

    /**
     * @var Guzzle
     */
    protected Guzzle $guzzle;

    public const CURATION_GRAPHQL_TOGGLE_PROD_URL = 'https://kube-general-feature-toggles.service.intraiad1.consul.csnzoo.com/api/client/features/curation_data_graphql_decoupling';
    public const CURATION_GRAPHQL_TOGGLE_DEV_URL = 'https://kube-general-feature-toggles.service.intradsm1.consul.csnzoo.com/api/client/features/curation_data_graphql_decoupling';

    // You'll need to get these auth tokens from your Unleash admin
    // Format: project-name:environment.token
    public const CURATION_GRAPHQL_TOGGLE_PROD_AUTH = 'brand-workflows-curation-tool:production.YOUR_PRODUCTION_TOKEN_HERE';
    public const CURATION_GRAPHQL_TOGGLE_DEV_AUTH = 'brand-workflows-curation-tool:development.YOUR_DEVELOPMENT_TOKEN_HERE';

    public function __construct(string $environment, Guzzle $guzzle = null)
    {
        $this->url = $environment === Environment::PRODUCTION ? self::CURATION_GRAPHQL_TOGGLE_PROD_URL : self::CURATION_GRAPHQL_TOGGLE_DEV_URL;
        $this->auth = $environment === Environment::PRODUCTION ? self::CURATION_GRAPHQL_TOGGLE_PROD_AUTH : self::CURATION_GRAPHQL_TOGGLE_DEV_AUTH;
        $this->guzzle = $guzzle ?? new Guzzle(['verify' => false]);
    }

    /**
     * Check if GraphQL decoupling is enabled
     *
     * @return bool
     */
    public function isGraphQLDecouplingEnabled(): bool
    {
        $url = $this->url;
        $headers = [
            'Authorization' => $this->auth,
            'Content-Type' => 'application/json'
        ];
        $timeout = 30;

        try {
            $res = $this->guzzle->request(
                'GET',
                $url,
                [
                    'headers' => $headers,
                    'timeout' => $timeout,
                ]
            );

            $contents = $res->getBody()->getContents();
            $response = json_decode($contents, true);

            // Check if the feature is enabled
            if (isset($response['enabled']) && $response['enabled'] === true) {
                // If there are strategies, check rollout percentage
                if (isset($response['strategies']) && !empty($response['strategies'])) {
                    $strategy = $response['strategies'][0];
                    if (isset($strategy['parameters']['rollout'])) {
                        $rollout = (int) $strategy['parameters']['rollout'];
                        return $rollout > 0; // Enable if rollout > 0%
                    }
                }
                return true; // Enabled without specific rollout strategy
            }

            return false;
        } catch (GuzzleException $e) {
            $this->error('Failed to fetch GraphQL decoupling toggle from Unleash', [
                'error' => $e->getMessage(),
                'url' => $url
            ]);
            return false; // Default to disabled if Unleash is unavailable
        }
    }

    /**
     * Get the current rollout percentage (for monitoring)
     *
     * @return int
     */
    public function getRolloutPercentage(): int
    {
        $url = $this->url;
        $headers = [
            'Authorization' => $this->auth,
            'Content-Type' => 'application/json'
        ];
        $timeout = 30;

        try {
            $res = $this->guzzle->request(
                'GET',
                $url,
                [
                    'headers' => $headers,
                    'timeout' => $timeout,
                ]
            );

            $contents = $res->getBody()->getContents();
            $response = json_decode($contents, true);

            if (isset($response['strategies']) && !empty($response['strategies'])) {
                $strategy = $response['strategies'][0];
                if (isset($strategy['parameters']['rollout'])) {
                    return (int) $strategy['parameters']['rollout'];
                }
            }

            return $response['enabled'] ? 100 : 0;
        } catch (GuzzleException $e) {
            $this->error('Failed to fetch rollout percentage from Unleash', [
                'error' => $e->getMessage(),
                'url' => $url
            ]);
            return 0;
        }
    }
}
