<?php

declare(strict_types=1);

/**
 * Test for CurationProductGraphService
 * 
 * <AUTHOR> for MTBW-4912
 * @copyright 2024 Wayfair LLC - All rights reserved
 */

namespace App\Tests\Unit\Infrastructure\Connection\Graphql;

use App\Infrastructure\Connection\Graphql\CurationProductGraphService;
use App\Infrastructure\Connection\Graphql\GraphClient;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

class CurationProductGraphService_Test extends TestCase
{
    private CurationProductGraphService $service;
    private MockObject $mockGraphClient;

    protected function setUp(): void
    {
        $this->mockGraphClient = $this->createMock(GraphClient::class);
        $this->service = new CurationProductGraphService($this->mockGraphClient);
    }

    public function testGetSkusMissingClass(): void
    {
        $skus = ['SKU001', 'SKU002', 'SKU003'];
        $expectedMissingSkus = ['SKU002'];

        $mockResponse = [
            'data' => [
                'products' => [
                    [
                        'sku' => 'SKU001',
                        'productClasses' => [
                            ['isMasterClass' => true]
                        ]
                    ],
                    [
                        'sku' => 'SKU002',
                        'productClasses' => [
                            ['isMasterClass' => false]
                        ]
                    ],
                    [
                        'sku' => 'SKU003',
                        'productClasses' => [
                            ['isMasterClass' => true]
                        ]
                    ]
                ]
            ]
        ];

        $this->mockGraphClient
            ->expects($this->once())
            ->method('getJsonResponseToQuery')
            ->with(
                $this->equalTo(CurationProductGraphService::GET_SKUS_MISSING_CLASS),
                $this->equalTo(['skus' => $skus]),
                $this->anything(),
                $this->equalTo(true),
                $this->equalTo(10)
            )
            ->willReturn($mockResponse);

        $result = $this->service->getSkusMissingClass($skus);

        $this->assertEquals($expectedMissingSkus, $result);
    }

    public function testGetSkusInvalidBrandCatalog(): void
    {
        $skus = ['SKU001', 'SKU002', 'SKU003'];
        $expectedInvalidSkus = ['SKU002'];

        $mockResponse = [
            'data' => [
                'products' => [
                    [
                        'sku' => 'SKU001',
                        'brandCatalogId' => 15
                    ],
                    [
                        'sku' => 'SKU002',
                        'brandCatalogId' => 0
                    ],
                    [
                        'sku' => 'SKU003',
                        'brandCatalogId' => 16
                    ]
                ]
            ]
        ];

        $this->mockGraphClient
            ->expects($this->once())
            ->method('getJsonResponseToQuery')
            ->willReturn($mockResponse);

        $result = $this->service->getSkusInvalidBrandCatalog($skus);

        $this->assertEquals($expectedInvalidSkus, $result);
    }

    public function testGetActiveProducts(): void
    {
        $skus = ['SKU001', 'SKU002', 'SKU003'];
        $expectedActiveSkus = ['SKU001', 'SKU003'];

        $mockResponse = [
            'data' => [
                'products' => [
                    [
                        'sku' => 'SKU001',
                        'status' => 2, // Active status
                        'additionalInfo' => [],
                        'productionTracking' => []
                    ],
                    [
                        'sku' => 'SKU002',
                        'status' => 1, // Inactive status
                        'additionalInfo' => [],
                        'productionTracking' => ['hasActiveTracking' => false]
                    ],
                    [
                        'sku' => 'SKU003',
                        'status' => 4, // Active status
                        'additionalInfo' => [],
                        'productionTracking' => []
                    ]
                ]
            ]
        ];

        $this->mockGraphClient
            ->expects($this->once())
            ->method('getJsonResponseToQuery')
            ->willReturn($mockResponse);

        $result = $this->service->getActiveProducts($skus);

        $this->assertEquals($expectedActiveSkus, $result);
    }

    public function testGetSkusWithKitData(): void
    {
        $skus = ['SKU001', 'SKU002'];
        $expectedKitData = [
            [
                'sku' => 'SKU001',
                'parent_sku' => 'PARENT001',
                'child_sku' => null
            ],
            [
                'sku' => 'SKU002',
                'parent_sku' => null,
                'child_sku' => 'CHILD002'
            ]
        ];

        $mockResponse = [
            'data' => [
                'products' => [
                    [
                        'sku' => 'SKU001',
                        'kitRelationships' => [
                            'parentSkus' => ['PARENT001'],
                            'childSkus' => []
                        ]
                    ],
                    [
                        'sku' => 'SKU002',
                        'kitRelationships' => [
                            'parentSkus' => [],
                            'childSkus' => ['CHILD002']
                        ]
                    ]
                ]
            ]
        ];

        $this->mockGraphClient
            ->expects($this->once())
            ->method('getJsonResponseToQuery')
            ->willReturn($mockResponse);

        $result = $this->service->getSkusWithKitData($skus);

        $this->assertEquals($expectedKitData, $result);
    }

    public function testEmptySkusReturnsEmptyArray(): void
    {
        $result = $this->service->getSkusMissingClass([]);
        $this->assertEquals([], $result);

        $result = $this->service->getSkusInvalidBrandCatalog([]);
        $this->assertEquals([], $result);

        $result = $this->service->getActiveProducts([]);
        $this->assertEquals([], $result);

        $result = $this->service->getSkusWithKitData([]);
        $this->assertEquals([], $result);
    }
}
