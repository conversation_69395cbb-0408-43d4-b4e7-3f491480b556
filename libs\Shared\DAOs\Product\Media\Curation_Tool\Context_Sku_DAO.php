<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\Product\Media\Curation_Tool;

use App\Domain\Model\FeatureToggle;
use App\Infrastructure\Connection\OrderConnection;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use PDO;
use WF\Shared\Helpers\SQL;

class Context_Sku_DAO {

  const LAST_365_DAYS = -365;

  private ProductConnection $pdo;
  private ProductConnection $pdo_psql;
  private FeatureTogglesInterface $featureToggles;

  /**
   * Context_Sku_DAO constructor.
   *
   * @param ProductConnection $pdo PDO
   * @param ProductConnection $pdo_psql PostgreSQL PDO
   * @param FeatureTogglesInterface $featureToggles Feature Toggles
   */
  public function __construct(ProductConnection $pdo, ProductConnection $pdo_psql, FeatureTogglesInterface $featureToggles) {
    $this->pdo = $pdo;
    $this->pdo_psql = $pdo_psql;
    $this->featureToggles = $featureToggles;
  }

  /**
   * @param string $sku SKU
   *
   * @return array
   * @throws \Exception
   */
  public function get_sku_data(string $sku) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      return $this->get_sku_data_psql($sku);
    } else {
      return $this->get_sku_data_sql($sku);
    }
  }

  /**
   * @param string $sku SKU
   *
   * @return array
   * @throws \Exception
   */
  private function get_sku_data_sql(string $sku) : array {
    $sql = '
    SELECT p.PrXnID, p.PrSKU, 0 AS PcoID, 0 AS CountTarget
    FROM csn_product.dbo.tblProduct p WITH(NOLOCK)
    WHERE p.PrSKU = :sku
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get SKU data');
    }

    return $statement->fetchAll();
  }

  /**
   * @param string $sku SKU
   *
   * @return array
   * @throws \Exception
   */
  private function get_sku_data_psql(string $sku) : array {
    $sql = '
    SELECT p."PrXnID", p."PrSKU", 0 AS "PcoID", 0 AS "CountTarget"
    FROM "tblProduct" p
    WHERE p."PrSKU" = :sku
    ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get SKU data from PostgreSQL');
    }

    return $statement->fetchAll();
  }

  /**
   * Get Status
   *
   * @param string $sku               SKU
   * @param int    $target_brand_type Target Brand Type (which BrwID it should look for)
   *
   * @return array
   * @throws \Exception
   */
  public function get_context_sku(string $sku, int $target_brand_type) : array {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      return $this->get_context_sku_psql($sku, $target_brand_type);
    } else {
      return $this->get_context_sku_sql($sku, $target_brand_type);
    }
  }

  /**
   * Get Status (SQL Server version)
   *
   * @param string $sku               SKU
   * @param int    $target_brand_type Target Brand Type (which BrwID it should look for)
   *
   * @return array
   * @throws \Exception
   */
  private function get_context_sku_sql(string $sku, int $target_brand_type) : array {
    $sql = '
      IF OBJECT_ID(\'tempdb.dbo.#SKUInput\') IS NOT NULL DROP TABLE #SKUInput
      CREATE TABLE #SKUInput (SKU  NVARCHAR(8) PRIMARY KEY)

      IF OBJECT_ID(\'tempdb.dbo.#SKUBestVersion\') IS NOT NULL DROP TABLE #SKUBestVersion
      CREATE TABLE #SKUBestVersion (SKU  NVARCHAR(8) PRIMARY KEY)

      IF OBJECT_ID(\'tempdb.dbo.#Consoliations\') IS NOT NULL DROP TABLE #Consoliations
      CREATE TABLE #Consoliations (PcoID INT, PcoSourcePrSKU  NVARCHAR(8), PcoTargetPrSKU  NVARCHAR(8), Lvl INT)

      IF OBJECT_ID(\'tempdb.dbo.#Collections\') IS NOT NULL DROP TABLE #Collections
      CREATE TABLE #Collections (XnID INT, PrSKU  NVARCHAR(8) PRIMARY KEY, PcoID INT)

      DECLARE @SKU NVARCHAR(8) = :sku
      DECLARE @TargetBrandType INT = :target_brand_type

      INSERT INTO #SKUInput (SKU) VALUES (@SKU)

      -- If Non WL SKU is input, use a collection of it
      IF(EXISTS(SELECT TOP 1 1 FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
                JOIN csn_product.dbo.tblManufacturer m WITH (NOLOCK) ON m.MaID = p.PrMaID
                WHERE p.PrSKU = @SKU AND m.MaBrwID = @TargetBrandType))
      BEGIN
            SELECT  ISNULL(p.PrXnID, 0) as PrXnID, p.PrSKU, 0 AS PcoID, 0 AS CountTarget, 0 as IsWhiteLabel
            FROM csn_product.dbo.tblProduct p WITH(NOLOCK)
            WHERE p.PrSKU = @SKU

            RETURN
      END

      --All the most recent NON WL SKU sources
      ;WITH tblParent AS
      (
        SELECT cParent.PcoID, cParent.PcoSourcePrSKU, cParent.PcoTargetPrSKU, 1 AS lvl, 1 AS Number
        FROM csn_product.dbo.tblProductConsolidation as cParent WITH (NOLOCK)
          JOIN #SKUInput ON SKU = PcoTargetPrSKU
          JOIN csn_product.dbo.tblProduct product WITH(NOLOCK) ON product.PrSKU = PcoTargetPrSKU
          JOIN csn_product.dbo.tblManufacturer m WITH(NOLOCK) ON m.MaID = product.PrMaID
        WHERE m.MaBrwID > 0
              AND PcoSourcePrSKU <> PcoTargetPrSKU
        UNION ALL
        SELECT cChild.PcoID, cChild.PcoSourcePrSKU, cChild.PcoTargetPrSKU, tblParent.lvl + 1 as lvl,  Number + 1
        FROM csn_product.dbo.tblProductConsolidation as cChild WITH (NOLOCK)
          JOIN tblParent ON tblParent.PcoSourcePrSKU = cChild.PcoTargetPrSKU
                            AND tblParent.PcoSourcePrSKU <> tblParent.PcoTargetPrSKU
                            AND cChild.PcoSourcePrSKU <> @SKU
        WHERE Number < 20 -- limit of nested joins
      )
      INSERT INTO #Consoliations (PcoID, PcoSourcePrSKU, PcoTargetPrSKU, Lvl)
      SELECT TOP 10000 pc.PcoID, pc.PcoSourcePrSKU, pc.PcoTargetPrSKU, pc.Lvl
      FROM tblParent pc
      OPTION(MAXRECURSION 30)

      INSERT INTO #Collections (XnID, PrSKU, PcoID)
        SELECT DISTINCT te.PrXnID, te.PcoSourcePrSKU, PcoID
        FROM (
               SELECT product.PrXnID, consolidations.PcoSourcePrSKU, lvl, MAX(consolidations.PcoID) as PcoID, DENSE_RANK() OVER (ORDER BY lvl) as levelEnd
               FROM #Consoliations consolidations
                 JOIN csn_product.dbo.tblProduct product WITH(NOLOCK) ON product.PrSKU = consolidations.PcoSourcePrSKU
                 JOIN csn_product.dbo.tblManufacturer m WITH(NOLOCK) ON m.MaID = product.PrMaID
               WHERE m.MaBrwID = @TargetBrandType
               GROUP BY product.PrXnID, consolidations.PcoSourcePrSKU, lvl
             ) as te
        WHERE te.levelEnd = 1

      -- Context Collection ID Results
      SELECT ISNULL(x.XnID, 0) as PrXnID, x.PrSKU, x.PcoID, COUNT(cr1.MaxPco) as CountTarget
      FROM #Collections x
      OUTER APPLY (
                      SELECT MAX(c1.PcoID) as MaxPco
                      FROM csn_product.dbo.tblProductConsolidation c1 WITH (NOLOCK)
                      WHERE c1.PcoSourcePrSKU = x.PrSKU
                      GROUP BY c1.PcoTargetPrSKU
                    ) cr1
      WHERE ISNULL(x.XnID, 0) > 0
      GROUP BY x.XnID, x.PrSKU, x.PcoID
      ';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);
    $statement->bindValue(':target_brand_type', $target_brand_type, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get context SKU');
    }

    return $statement->fetchAll();
  }

  /**
   * Get Status (PostgreSQL version)
   *
   * @param string $sku               SKU
   * @param int    $target_brand_type Target Brand Type (which BrwID it should look for)
   *
   * @return array
   * @throws \Exception
   */
  private function get_context_sku_psql(string $sku, int $target_brand_type) : array {
    // Simplified PostgreSQL version - this complex logic may need to be refactored
    // For now, implementing a basic version that returns the product's collection ID
    $sql = '
      SELECT COALESCE(p."PrXnID", 0) as "PrXnID", p."PrSKU", 0 AS "PcoID", 0 AS "CountTarget",
             CASE WHEN m."MaBrwID" = :target_brand_type THEN 0 ELSE 1 END as "IsWhiteLabel"
      FROM "tblProduct" p
      JOIN "tblManufacturer" m ON m."MaID" = p."PrMaID"
      WHERE p."PrSKU" = :sku
    ';

    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);
    $statement->bindValue(':target_brand_type', $target_brand_type, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get context SKU from PostgreSQL');
    }

    return $statement->fetchAll();
  }

  /**
   * Saves calculated mapping between target sku and its context sku
   *
   * @param string   $target_sku  target sku
   * @param string   $context_sku context sku
   * @param int|null $xnid        collection id
   *
   * @return void
   * @throws \Exception
   */
  public function upsert_context_collection_sku(string $target_sku, string $context_sku, int $xnid = null) {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      $this->upsert_context_collection_sku_psql($target_sku, $context_sku, $xnid);
    } else {
      $this->upsert_context_collection_sku_sql($target_sku, $context_sku, $xnid);
    }
  }

  /**
   * Saves calculated mapping between target sku and its context sku (SQL Server version)
   *
   * @param string   $target_sku  target sku
   * @param string   $context_sku context sku
   * @param int|null $xnid        collection id
   *
   * @return void
   * @throws \Exception
   */
  private function upsert_context_collection_sku_sql(string $target_sku, string $context_sku, int $xnid = null) {
    $sql = '
      DECLARE @targetSku NVARCHAR(8) = :target_sku
      IF NOT EXISTS(SELECT TOP 1 1 FROM csn_product.dbo.tblContextCollectionSKU WITH(NOLOCK) WHERE TargetSKU = @targetSku)
        BEGIN
          INSERT INTO csn_product.dbo.tblContextCollectionSKU(TargetSKU, ContextSKU, XnID)
            VALUES (@targetSku, :context_sku, :xnid)
        END
    ';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':target_sku', $target_sku, PDO::PARAM_STR);
    $statement->bindValue(':context_sku', $context_sku, PDO::PARAM_STR);
    $statement->bindValue(':xnid', $xnid !== 0 ? $xnid : null, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot save context collection sku');
    }
  }

  /**
   * Saves calculated mapping between target sku and its context sku (PostgreSQL version)
   *
   * @param string   $target_sku  target sku
   * @param string   $context_sku context sku
   * @param int|null $xnid        collection id
   *
   * @return void
   * @throws \Exception
   */
  private function upsert_context_collection_sku_psql(string $target_sku, string $context_sku, int $xnid = null) {
    $sql = '
      INSERT INTO "tblContextCollectionSKU" ("TargetSKU", "ContextSKU", "XnID")
      VALUES (:target_sku, :context_sku, :xnid)
      ON CONFLICT ("TargetSKU") DO NOTHING
    ';

    $statement = $this->pdo_psql->prepare($sql);

    $statement->bindValue(':target_sku', $target_sku, PDO::PARAM_STR);
    $statement->bindValue(':context_sku', $context_sku, PDO::PARAM_STR);
    $statement->bindValue(':xnid', $xnid !== 0 ? $xnid : null, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot save context collection sku to PostgreSQL');
    }
  }

  /**
   * @param string $sku target sku
   *
   * @return int|null
   */
  public function get_xn_id_from_original_data(string $sku) {
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      return $this->get_xn_id_from_original_data_psql($sku);
    } else {
      return $this->get_xn_id_from_original_data_sql($sku);
    }
  }

  /**
   * @param string $sku target sku
   *
   * @return int|null
   */
  private function get_xn_id_from_original_data_sql(string $sku) {
    $sql = '
      SELECT global_product.GblPrXnID
      FROM csn_product.dbo.tblGblProduct global_product WITH (NOLOCK)
      WHERE global_product.GblPrSKU = :sku
      AND global_product.GblBclgID IN (15, 16, 17)
    ';

    $statement = $this->pdo->prepare($sql);

    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get collection Id from original data');
    }

    return $statement->fetch(PDO::FETCH_COLUMN);
  }

  /**
   * @param string $sku target sku
   *
   * @return int|null
   */
  private function get_xn_id_from_original_data_psql(string $sku) {
    $sql = '
      SELECT global_product."GblPrXnID"
      FROM "tblGblProduct" global_product
      WHERE global_product."GblPrSKU" = :sku
      AND global_product."GblBclgID" IN (15, 16, 17)
    ';

    $statement = $this->pdo_psql->prepare($sql);

    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get collection Id from original data from PostgreSQL');
    }

    return $statement->fetch(PDO::FETCH_COLUMN);
  }
}
}
