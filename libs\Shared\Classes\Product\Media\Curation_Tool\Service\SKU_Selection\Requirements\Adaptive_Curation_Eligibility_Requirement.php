<?php
/**
 * PHP version 8.1
 *
 * Adaptive Curation Eligibility Requirement
 * Uses feature toggle to switch between old and new eligibility conditions
 *
 * <AUTHOR> for eligibility conditions update
 * @copyright 2024 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Requirements;

use App\Infrastructure\Connection\UnleashFeatureToggle\UpdatedCurationEligibilityToggle;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model;
use Psr\Log\LoggerInterface;

class Adaptive_Curation_Eligibility_Requirement implements SKU_Eligibility_Requirement_Interface {

  private UpdatedCurationEligibilityToggle $featureToggle;
  private Loader_Eligibility_Requirement $oldRequirement;
  private Updated_Curation_Eligibility_Requirement $newRequirement;
  private LoggerInterface $logger;

  public function __construct(
    UpdatedCurationEligibilityToggle $featureToggle,
    Loader_Eligibility_Requirement $oldRequirement,
    Updated_Curation_Eligibility_Requirement $newRequirement,
    LoggerInterface $logger
  ) {
    $this->featureToggle = $featureToggle;
    $this->oldRequirement = $oldRequirement;
    $this->newRequirement = $newRequirement;
    $this->logger = $logger;
  }

  /**
   * Check if SKU is eligible using either old or new conditions based on feature toggle
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku Request SKU
   *
   * @return bool
   */
  public function is_eligible(Curation_Request_SKU_Base_Model $request_sku) : bool {
    $useNewConditions = $this->featureToggle->isUpdatedEligibilityEnabled();
    
    try {
      if ($useNewConditions) {
        $result = $this->newRequirement->is_eligible($request_sku);
        $this->logEligibilityCheck($request_sku, 'new', $result);
        return $result;
      } else {
        $result = $this->oldRequirement->is_eligible($request_sku);
        $this->logEligibilityCheck($request_sku, 'old', $result);
        return $result;
      }
    } catch (\Exception $e) {
      $this->logger->error('Adaptive eligibility requirement: Exception occurred', [
        'sku' => $request_sku->get_sku(),
        'use_new_conditions' => $useNewConditions,
        'error' => $e->getMessage()
      ]);
      
      // Fallback to old requirement on error
      return $this->oldRequirement->is_eligible($request_sku);
    }
  }

  /**
   * Get detailed comparison between old and new eligibility results
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku Request SKU
   * @return array
   */
  public function getEligibilityComparison(Curation_Request_SKU_Base_Model $request_sku) : array {
    $toggleStatus = $this->featureToggle->isUpdatedEligibilityEnabled();
    
    try {
      $oldResult = $this->oldRequirement->is_eligible($request_sku);
      $newResult = $this->newRequirement->is_eligible($request_sku);
      
      $comparison = [
        'sku' => $request_sku->get_sku(),
        'feature_toggle_enabled' => $toggleStatus,
        'rollout_percentage' => $this->featureToggle->getRolloutPercentage(),
        'old_conditions' => [
          'result' => $oldResult,
          'implementation' => 'Loader_Eligibility_Requirement',
          'conditions_checked' => [
            'is_master_core_class',
            'is_standard_brand', 
            'has_images',
            '!is_holdout_manufacturer',
            'is_wayfair_channel',
            'is_active_join_supplier',
            'is_right_assigned_supplier_method',
            '!is_perigold_only',
            '!has_holdout_manufacturer_part'
          ]
        ],
        'new_conditions' => [
          'result' => $newResult,
          'implementation' => 'Updated_Curation_Eligibility_Requirement',
          'conditions_checked' => [
            'is_master_core_class',
            '!is_holdout_manufacturer',
            '!is_perigold_only',
            'has_images'
          ],
          'detailed_breakdown' => $this->newRequirement->get_eligibility_breakdown($request_sku)
        ],
        'comparison' => [
          'results_match' => $oldResult === $newResult,
          'active_implementation' => $toggleStatus ? 'new' : 'old',
          'active_result' => $toggleStatus ? $newResult : $oldResult
        ],
        'timestamp' => date('Y-m-d H:i:s')
      ];

      if ($oldResult !== $newResult) {
        $comparison['difference_analysis'] = [
          'old_excluded_reasons' => $this->getOldExcludedReasons($request_sku),
          'new_excluded_reasons' => $this->newRequirement->get_excluded_reasons($request_sku),
          'impact' => $newResult ? 'SKU becomes eligible with new conditions' : 'SKU becomes ineligible with new conditions'
        ];
      }

      return $comparison;

    } catch (\Exception $e) {
      return [
        'sku' => $request_sku->get_sku(),
        'error' => 'Failed to generate comparison: ' . $e->getMessage(),
        'feature_toggle_enabled' => $toggleStatus,
        'timestamp' => date('Y-m-d H:i:s')
      ];
    }
  }

  /**
   * Log eligibility check for monitoring
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku
   * @param string $implementation
   * @param bool $result
   */
  private function logEligibilityCheck(Curation_Request_SKU_Base_Model $request_sku, string $implementation, bool $result): void {
    $this->logger->info('Adaptive eligibility check', [
      'sku' => $request_sku->get_sku(),
      'implementation_used' => $implementation,
      'eligible' => $result,
      'rollout_percentage' => $this->featureToggle->getRolloutPercentage()
    ]);
  }

  /**
   * Get excluded reasons using old requirement logic
   *
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model $request_sku
   * @return string
   */
  private function getOldExcludedReasons(Curation_Request_SKU_Base_Model $request_sku): string {
    // This mirrors the logic from the old requirement
    $reasons = [];

    if (!$request_sku->is_master_core_class()) {
      $reasons[] = "Is not master core class";
    }
    if (!$request_sku->is_standard_brand()) {
      $reasons[] = "Is not standard brand";
    }
    if (!$request_sku->has_images()) {
      $reasons[] = "Has no images";
    }
    if ($request_sku->is_holdout_manufacturer()) {
      $reasons[] = "Is holdout manufacturer";
    }
    if (!$request_sku->is_wayfair_channel()) {
      $reasons[] = "Is not Wayfair channel";
    }
    if (!$request_sku->is_active_join_supplier()) {
      $reasons[] = "Is not active join supplier";
    }
    if (!$request_sku->is_right_assigned_supplier_method()) {
      $reasons[] = "Is not right assigned supplier method";
    }
    if ($request_sku->is_perigold_only()) {
      $reasons[] = "Is Perigold only";
    }
    if ($request_sku->has_holdout_manufacturer_part()) {
      $reasons[] = "Has holdout manufacturer part";
    }

    return empty($reasons) ? "Eligible" : implode("; ", $reasons);
  }
}
