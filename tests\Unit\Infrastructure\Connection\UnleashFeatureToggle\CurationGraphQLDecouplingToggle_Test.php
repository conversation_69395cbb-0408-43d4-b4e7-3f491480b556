<?php

declare(strict_types=1);

/**
 * Test for CurationGraphQLDecouplingToggle
 * 
 * <AUTHOR> for MTBW-4912
 * @copyright 2024 Wayfair LLC - All rights reserved
 */

namespace App\Tests\Unit\Infrastructure\Connection\UnleashFeatureToggle;

use App\Infrastructure\Connection\UnleashFeatureToggle\CurationGraphQLDecouplingToggle;
use GuzzleHttp\Client as Guzzle;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Psr7\Response;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use WF\Shared\Environment;

class CurationGraphQLDecouplingToggle_Test extends TestCase
{
    private CurationGraphQLDecouplingToggle $toggle;
    private MockObject $mockGuzzle;

    protected function setUp(): void
    {
        $this->mockGuzzle = $this->createMock(Guzzle::class);
        $this->toggle = new CurationGraphQLDecouplingToggle(Environment::DEVELOPMENT, $this->mockGuzzle);
    }

    public function testIsGraphQLDecouplingEnabledWithRollout100(): void
    {
        $responseBody = json_encode([
            'enabled' => true,
            'strategies' => [
                [
                    'parameters' => [
                        'rollout' => '100'
                    ]
                ]
            ]
        ]);

        $mockResponse = $this->createMock(Response::class);
        $mockResponse->method('getBody->getContents')->willReturn($responseBody);

        $this->mockGuzzle
            ->expects($this->once())
            ->method('request')
            ->with(
                'GET',
                CurationGraphQLDecouplingToggle::CURATION_GRAPHQL_TOGGLE_DEV_URL,
                $this->callback(function ($options) {
                    return isset($options['headers']['Authorization']) &&
                           isset($options['headers']['Content-Type']) &&
                           $options['timeout'] === 30;
                })
            )
            ->willReturn($mockResponse);

        $result = $this->toggle->isGraphQLDecouplingEnabled();

        $this->assertTrue($result);
    }

    public function testIsGraphQLDecouplingEnabledWithRollout0(): void
    {
        $responseBody = json_encode([
            'enabled' => true,
            'strategies' => [
                [
                    'parameters' => [
                        'rollout' => '0'
                    ]
                ]
            ]
        ]);

        $mockResponse = $this->createMock(Response::class);
        $mockResponse->method('getBody->getContents')->willReturn($responseBody);

        $this->mockGuzzle
            ->expects($this->once())
            ->method('request')
            ->willReturn($mockResponse);

        $result = $this->toggle->isGraphQLDecouplingEnabled();

        $this->assertFalse($result);
    }

    public function testIsGraphQLDecouplingEnabledWhenDisabled(): void
    {
        $responseBody = json_encode([
            'enabled' => false
        ]);

        $mockResponse = $this->createMock(Response::class);
        $mockResponse->method('getBody->getContents')->willReturn($responseBody);

        $this->mockGuzzle
            ->expects($this->once())
            ->method('request')
            ->willReturn($mockResponse);

        $result = $this->toggle->isGraphQLDecouplingEnabled();

        $this->assertFalse($result);
    }

    public function testIsGraphQLDecouplingEnabledWithGuzzleException(): void
    {
        $this->mockGuzzle
            ->expects($this->once())
            ->method('request')
            ->willThrowException(new GuzzleException('Network error'));

        $result = $this->toggle->isGraphQLDecouplingEnabled();

        $this->assertFalse($result); // Should default to false on error
    }

    public function testGetRolloutPercentage(): void
    {
        $responseBody = json_encode([
            'enabled' => true,
            'strategies' => [
                [
                    'parameters' => [
                        'rollout' => '75'
                    ]
                ]
            ]
        ]);

        $mockResponse = $this->createMock(Response::class);
        $mockResponse->method('getBody->getContents')->willReturn($responseBody);

        $this->mockGuzzle
            ->expects($this->once())
            ->method('request')
            ->willReturn($mockResponse);

        $result = $this->toggle->getRolloutPercentage();

        $this->assertEquals(75, $result);
    }

    public function testGetRolloutPercentageWithNoStrategies(): void
    {
        $responseBody = json_encode([
            'enabled' => true
        ]);

        $mockResponse = $this->createMock(Response::class);
        $mockResponse->method('getBody->getContents')->willReturn($responseBody);

        $this->mockGuzzle
            ->expects($this->once())
            ->method('request')
            ->willReturn($mockResponse);

        $result = $this->toggle->getRolloutPercentage();

        $this->assertEquals(100, $result); // Should return 100 if enabled without strategies
    }

    public function testProductionEnvironmentUsesCorrectUrls(): void
    {
        $productionToggle = new CurationGraphQLDecouplingToggle(Environment::PRODUCTION, $this->mockGuzzle);

        // Use reflection to access private properties for testing
        $reflection = new \ReflectionClass($productionToggle);
        $urlProperty = $reflection->getProperty('url');
        $urlProperty->setAccessible(true);
        $authProperty = $reflection->getProperty('auth');
        $authProperty->setAccessible(true);

        $this->assertEquals(CurationGraphQLDecouplingToggle::CURATION_GRAPHQL_TOGGLE_PROD_URL, $urlProperty->getValue($productionToggle));
        $this->assertEquals(CurationGraphQLDecouplingToggle::CURATION_GRAPHQL_TOGGLE_PROD_AUTH, $authProperty->getValue($productionToggle));
    }
}
