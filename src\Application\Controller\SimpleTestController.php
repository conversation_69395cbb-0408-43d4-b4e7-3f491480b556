<?php

declare(strict_types=1);

/**
 * Simple test controller to verify routing works
 * 
 * <AUTHOR> for MTBW-4912
 * @copyright 2024 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class SimpleTestController extends AbstractController
{
    /**
     * Simple health check endpoint
     *
     * @Route("/api/test/health", name="simple_test_health", methods={"GET"})
     */
    public function healthCheck(): JsonResponse
    {
        return new JsonResponse([
            'success' => true,
            'message' => 'API routing is working',
            'timestamp' => date('Y-m-d H:i:s'),
            'environment' => $_ENV['WF_ENV'] ?? 'unknown'
        ]);
    }

    /**
     * Test endpoint with app root route
     *
     * @Route("/api/test/info", name="simple_test_info", methods={"GET"})
     */
    public function getInfo(): JsonResponse
    {
        return new JsonResponse([
            'success' => true,
            'data' => [
                'app_name' => 'brand-workflows-curation-tool',
                'app_root_route' => '/d/curation-tool',
                'current_path' => '/api/test/info',
                'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
                'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
            ]
        ]);
    }
}
