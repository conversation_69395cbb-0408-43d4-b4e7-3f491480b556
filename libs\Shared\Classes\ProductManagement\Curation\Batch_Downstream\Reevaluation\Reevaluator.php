<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation;

use App\Domain\Model\FeatureToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use Psr\Log\LoggerInterface;
use WF\Shared\Classes\Product\Media\Curation_Tool\Factory\SKU_Selection\Curation_Request_Factory;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Curation_Request_Service;
use WF\Shared\DAOs\Product\Media\Batch_Verification_Postgres_DAO;
use WF\Shared\Models\Product\Media\Batch_Verification_Item_Model;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Base_Model;
use WF\Shared\Models\ProductManagement\White<PERSON>\Batching\Batch;
use WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU;
use WF\Shared\DAOs\Product\Media\Batch_Verification_MSSQL_DAO;
use WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_SKU_Model;
use WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Curation_Decision_Logging_Service;

class Reevaluator implements Reevaluator_Interface {
  /**
   * @var \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Grouper
   */
  private $grouper;

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Factory\SKU_Selection\Curation_Request_Factory
   */
  private $curation_request_factory;

  /**
   * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Curation_Request_Service
   */
  private $curation_request_service;

  /**
   * @var \WF\Shared\DAOs\Product\Media\Batch_Verification_MSSQL_DAO
   */
  private $dao;

  /**
   * @var \WF\Shared\DAOs\Product\Media\Batch_Verification_Postgres_DAO
   */
  private $dao_psql;

  private FeatureTogglesInterface $featureToggles;

  /**
   * @var LoggerInterface
   */
  private $logger;

    /**
     * @var \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Curation_Decision_Logging_Service
     */
  private $curation_decision_logging_service;


  /**
   * Reevaluator constructor.
   *
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Grouper $grouper                  Reevaluation_Sku_Grouper
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Factory\SKU_Selection\Curation_Request_Factory        $curation_request_factory Curation_Request_Factory
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Curation_Request_Service        $curation_request_service Curation_Request_Service
   * @param \WF\Shared\DAOs\Product\Media\Batch_Verification_MSSQL_DAO                                           $dao                      Batch_Verification_MSSQL_DAO
   * @param LoggerInterface                                                                                      $logger                   Logger
   * @param \WF\Shared\Classes\Product\Media\Curation_Tool\Service\SKU_Selection\Curation_Decision_Logging_Service $curation_decision_logging_service Curation_Decision_Logging_Service
   * @param \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Decision_Logging_Model            $curation_decision_logging_model Curation_Decision_Logging_Model
   */
  public function __construct(
      Reevaluation_Sku_Grouper $grouper,
      Curation_Request_Factory $curation_request_factory,
      Curation_Request_Service $curation_request_service,
      Batch_Verification_MSSQL_DAO $dao,
      Batch_Verification_Postgres_DAO $dao_psql,
      FeatureTogglesInterface $featureToggles,
      LoggerInterface $logger,
      Curation_Decision_Logging_Service $curation_decision_logging_service
    ) {
    $this->grouper                  = $grouper;
    $this->curation_request_factory = $curation_request_factory;
    $this->curation_request_service = $curation_request_service;
    $this->dao                      = $dao;
    $this->dao_psql                 = $dao_psql;
    $this->featureToggles           = $featureToggles;
    $this->logger                   = $logger;
    $this->curation_decision_logging_service = $curation_decision_logging_service;
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch The batch that should be reevaluated
   *
   * @return void
   */
  public function reevaluate(Batch $batch) {
    $this->recalculate_skus_eligibility($batch);

    $groups = $this->grouper->create_groups($batch);

    foreach ($groups as $group) {
      $this->logger->info('Group reevaluating', ['group' => $group->to_string()]);

      if ($this->should_exclude_group($group)) {
        $this->exclude($group);
      }
    }
  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group $group Reevaluation_Sku_Group
   *
   * @return void
   */
  private function exclude(Reevaluation_Sku_Group $group) {
    $ids  = [];
    $skus = [];
    foreach ($group->get_skus() as $sku) {
      if (!$this->should_reevaluate_verified_sku($sku)) {
        continue;
      }

      if ($sku->is_eligible()) {
        continue;
      }

      $sku->set_is_excluded_from_wl(1);
      $excluded_reason = $sku->get_excluded_reason();
      $sku_id = $sku->get_id();
      $this->logger->info('Automatically Excluded sku:'.$sku->get_sku().' excluded reason:' . $excluded_reason );
      $this->dao_psql->update_items_exclude_reason(
          Batch_Verification_Item_Model::EXCLUDED_REASON_AUTOMATED_EXCLUSION_NOT_ELIGIBLE_ANYMORE,
          $excluded_reason,
          $sku_id
      );
    }

  }

  /**
   * @param \WF\Shared\Classes\ProductManagement\Curation\Batch_Downstream\Reevaluation\Reevaluation_Sku_Group $group Reevaluation_Sku_Group
   *
   * @return bool
   */
  private function should_exclude_group(Reevaluation_Sku_Group $group) : bool {
    $should_exclude = false;

    foreach ($group->get_skus() as $sku) {
      if (!$this->should_reevaluate_verified_sku($sku)) {
        continue;
      }

      // if there is a sku in the group still eligible, group cannot be excluded
      if ($sku->is_eligible()) {
        $should_exclude = false;
        break;
      }

      // there is at least one SKU is no longer elibigle in the group
      $should_exclude = true;
    }

    return $should_exclude;
  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch The batch that should be reevaluated
   *
   * @return void
   */
  private function recalculate_skus_eligibility(Batch $batch) {
    $curation_request = $this->load_curation_request($batch);
    $curation_decision_logging_model_list = [];


    foreach ($curation_request->get_sku_list() as $sku) {

      /**
       * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $verified_sku
       */
      $verified_sku = $batch->get_sku($sku->get_sku());

      if ($verified_sku === null) { /** @phpstan-ignore-line */
        $this->logger->error(
            sprintf(
                'SKU "%s" does not exist in the batch %s',
                $sku->get_sku(),
                $batch->get_id()
            )
        );

        continue;
      }
      $this->logger->info(
          sprintf(
              '***************************** SKU "%s" 
              Eligibility_Status: %s 
              IsEligible: %s
              is_holdout_manufacturer: %s
              has_holdout_manufacturer_part: %s
              brand_catalog_id: %s
              collection_id: %s
              has_images: %s
              is_active_join_supplier: %s
              is_kit_pr_sku: %s
              is_master_core_class: %s
              is_perigold_only: %s
              is_right_assigned_supplier_method: %s
              is_right_product_status: %s
              is_standard_brand: %s
              is_wayfair_channel: %s
              is_eu: %s
              PrStatus: %s',
              $sku->get_sku(),
              $sku->get_eligibility_status(),
              $sku->is_eligible(),
              $sku->is_holdout_manufacturer(),
              $sku->has_holdout_manufacturer_part(),
              $sku->get_brand_catalog_id(),
              $sku->get_collection_id(),
              $sku->has_images(),
              $sku->is_active_join_supplier(),
              $sku->is_kit_pr_sku(),
              $sku->is_master_core_class(),
              $sku->is_perigold_only(),
              $sku->is_right_assigned_supplier_method(),
              $sku->is_right_product_status(),
              $sku->is_standard_brand(),
              $sku->is_wayfair_channel(),
              $sku->is_eu(),
              $sku->get_product_status()
              )
          );


      $this->logger->info('Automatically Excluded sku= '.$sku->get_sku().' eligibliity status=' . $sku->get_eligibility_status() );
      $verified_sku->set_eligible($sku->get_eligibility_status() !== Curation_Request_SKU_Base_Model::NOT_ELIGIBLE);

      if (!$verified_sku->is_eligible()) {
        $excludedReason = $sku->get_sku_excluded_reasons();
        $verified_sku->set_excluded_reason($excludedReason);
      }

      $this->curation_decision_logging_service->createListOfCurationDecisionLoggingModel($curation_decision_logging_model_list, $sku , $verified_sku, $batch);
    }
    $this->logger->info('Number of SKUs being inserted into BigQuery: ' . count($curation_decision_logging_model_list));
    try{
        $this->curation_decision_logging_service->insertAllSkusToBigQuery($curation_decision_logging_model_list);
    }
    catch (\Exception $e) {
            $failedSkus = array_map(
            fn($model) => $model->getSku(), // Assuming `getSku()` is a method in the model to retrieve the SKU
            $curation_decision_logging_model_list
        );

        $this->logger->error(
            'Error while inserting SKUs to BigQuery: ' . $e->getMessage(),
            ['failed_skus' => $failedSkus]
        );
    }

  }

  /**
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Batch $batch The batch that should be reevaluated
   *
   * @return \WF\Shared\Models\Product\Media\Curation_Tool\SKU_Selection\Curation_Request_Model
   */
  private function load_curation_request(Batch $batch) : Curation_Request_Model {
    $sku_list = [];

    /**
     * @var \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $verified_batch_sku
     */
    foreach ($batch->get_skus() as $verified_batch_sku) {
      if (!$this->should_reevaluate_verified_sku($verified_batch_sku)) {
          // kitparents, collisions, excluded ones not required to be evaluated.
        continue;
      }

      $sku_list[] = $verified_batch_sku->get_sku();
    }

    $curation_request          = $this->curation_request_factory->create_request_model();
    $curation_request->type_id = $batch->get_curation_request_type();

    $this->curation_request_service->load_skus_by_list($curation_request, $sku_list);

    return $curation_request;
  }

  /**
   * Checks if a verified SKU is eligible for Reevaluation (not a collision and not already excluded)
   *
   * @param \WF\Shared\Models\ProductManagement\WhiteLabel\Batching\Verified_Batch_SKU $verified_batch_sku Verified_Batch_SKU
   *
   * @return bool
   */
  private function should_reevaluate_verified_sku(Verified_Batch_SKU $verified_batch_sku) : bool {
      // TRUE if SKU is not kitparent,not collision, not excluded
      // FALSE if SKU is kitparent or collision or excluded by user
    return !$verified_batch_sku->is_kit_parent() && !$verified_batch_sku->is_collision() && !$verified_batch_sku->is_excluded_from_wl();
  }
}
