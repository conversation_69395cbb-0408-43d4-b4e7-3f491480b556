<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use App\Application\DTO\CurationRequest;
use App\Infrastructure\Connection\UnleashFeatureToggle\CurationGraphQLDecouplingToggle;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use App\Domain\Model\FeatureToggle;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Section_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Data_Consistency_Validation_DAO;

class DebugController extends AbstractBaseController
{
    /**
     * @Route(path="/debug_groups", methods={"GET"})
     * @param Curation_Section_Service $curation_section_service_with_collisions
     * @param CurationRequest $curationRequest
     * @return JsonResponse
     * @throws \Exception
     */
    public function debug_groups(
        Curation_Section_Service $curation_section_service_with_collisions,
        CurationRequest $curationRequest
    ): JsonResponse {
        $batchId = $curationRequest->getBatchId();

        $this->info('Loading Curation sections with collisions', ['batch_id' => $batchId]);
        $groups = $curation_section_service_with_collisions->load($batchId);

        return $this->json($groups);
    }

    /**
     * Test GraphQL feature toggle functionality
     *
     * @Route(path="/debug_graphql_toggle", methods={"GET"})
     * @param FeatureTogglesInterface $featureToggles
     * @return JsonResponse
     */
    public function debug_graphql_toggle(FeatureTogglesInterface $featureToggles): JsonResponse
    {
        try {
            // Test PuREST feature toggle (existing system)
            $purestToggleEnabled = $featureToggles->isEnabled(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING);

            $response = [
                'success' => true,
                'message' => 'GraphQL feature toggle test',
                'data' => [
                    'feature_toggle_name' => 'curation_data_graphql_decoupling',
                    'purest_toggle_enabled' => $purestToggleEnabled,
                    'timestamp' => date('Y-m-d H:i:s'),
                    'environment' => $_ENV['WF_ENV'] ?? 'unknown',
                    'test_type' => 'PuREST Integration'
                ]
            ];

            return $this->json($response);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => [
                    'message' => 'Failed to test GraphQL feature toggle',
                    'details' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            ], 500);
        }
    }

    /**
     * Test GraphQL decoupling with actual DAO
     *
     * @Route(path="/debug_graphql_dao_test", methods={"POST"})
     * @param Request $request
     * @param Curation_Data_Consistency_Validation_DAO $dao
     * @param FeatureTogglesInterface $featureToggles
     * @return JsonResponse
     */
    public function debug_graphql_dao_test(
        Request $request,
        Curation_Data_Consistency_Validation_DAO $dao,
        FeatureTogglesInterface $featureToggles
    ): JsonResponse {
        try {
            $data = json_decode($request->getContent(), true);
            $skus = $data['skus'] ?? ['TEST001', 'TEST002', 'TEST003'];

            $isToggleEnabled = $featureToggles->isEnabled(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING);

            $startTime = microtime(true);
            $result = $dao->get_skus_missing_class($skus);
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);

            return $this->json([
                'success' => true,
                'data' => [
                    'method' => 'get_skus_missing_class',
                    'input_skus' => $skus,
                    'result' => $result,
                    'feature_toggle_enabled' => $isToggleEnabled,
                    'implementation_used' => $isToggleEnabled ? 'GraphQL' : 'SQL',
                    'execution_time_ms' => $executionTime,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => [
                    'message' => 'Failed to test GraphQL DAO',
                    'details' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            ], 500);
        }
    }
}
