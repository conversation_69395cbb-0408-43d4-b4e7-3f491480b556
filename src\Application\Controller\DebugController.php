<?php

declare(strict_types=1);

/**
 * PHP version 8.1
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2018 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use App\Application\DTO\CurationRequest;
use App\Infrastructure\Connection\UnleashFeatureToggle\CurationGraphQLDecouplingToggle;
use App\Infrastructure\Connection\Graphql\CurationProductGraphService;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use App\Domain\Model\FeatureToggle;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Curation_Section_Service;
use WF\Extranet\Classes\Catalog\Exclusivity\Curation_Tool\Loading\Storage\Curation_Data_Consistency_Validation_DAO;

class DebugController extends AbstractBaseController
{
    /**
     * @Route(path="/debug_groups", methods={"GET"})
     * @param Curation_Section_Service $curation_section_service_with_collisions
     * @param CurationRequest $curationRequest
     * @param FeatureTogglesInterface $featureToggles
     * @return JsonResponse
     * @throws \Exception
     */
    public function debug_groups(
        Curation_Section_Service $curation_section_service_with_collisions,
        CurationRequest $curationRequest,
        FeatureTogglesInterface $featureToggles
    ): JsonResponse {
        $batchId = $curationRequest->getBatchId();

        // Test feature toggle if requested
        if (isset($_GET['test_toggle']) && $_GET['test_toggle'] === '1') {
            try {
                $isToggleEnabled = $featureToggles->isEnabled(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING);

                return $this->json([
                    'success' => true,
                    'message' => 'GraphQL feature toggle test via debug_groups',
                    'data' => [
                        'feature_toggle_name' => 'curation_data_graphql_decoupling',
                        'is_enabled' => $isToggleEnabled,
                        'timestamp' => date('Y-m-d H:i:s'),
                        'environment' => $_ENV['WF_ENV'] ?? 'unknown',
                        'test_method' => 'PuREST Integration via debug_groups'
                    ]
                ]);
            } catch (\Exception $e) {
                return $this->json([
                    'success' => false,
                    'error' => [
                        'message' => 'Feature toggle test failed',
                        'details' => $e->getMessage()
                    ]
                ], 500);
            }
        }

        $this->info('Loading Curation sections with collisions', ['batch_id' => $batchId]);
        $groups = $curation_section_service_with_collisions->load($batchId);

        return $this->json($groups);
    }

    /**
     * Test GraphQL feature toggle functionality
     *
     * @Route(path="/debug_graphql_toggle", methods={"GET"})
     * @param FeatureTogglesInterface $featureToggles
     * @return JsonResponse
     */
    public function debug_graphql_toggle(FeatureTogglesInterface $featureToggles): JsonResponse
    {
        try {
            // Test PuREST feature toggle (existing system)
            $purestToggleEnabled = $featureToggles->isEnabled(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING);

            $response = [
                'success' => true,
                'message' => 'GraphQL feature toggle test',
                'data' => [
                    'feature_toggle_name' => 'curation_data_graphql_decoupling',
                    'purest_toggle_enabled' => $purestToggleEnabled,
                    'timestamp' => date('Y-m-d H:i:s'),
                    'environment' => $_ENV['WF_ENV'] ?? 'unknown',
                    'test_type' => 'PuREST Integration'
                ]
            ];

            return $this->json($response);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => [
                    'message' => 'Failed to test GraphQL feature toggle',
                    'details' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            ], 500);
        }
    }

    /**
     * Test GraphQL decoupling with actual DAO
     *
     * @Route(path="/debug_graphql_dao_test", methods={"POST"})
     * @param Request $request
     * @param Curation_Data_Consistency_Validation_DAO $dao
     * @param FeatureTogglesInterface $featureToggles
     * @return JsonResponse
     */
    public function debug_graphql_dao_test(
        Request $request,
        Curation_Data_Consistency_Validation_DAO $dao,
        FeatureTogglesInterface $featureToggles
    ): JsonResponse {
        try {
            $data = json_decode($request->getContent(), true);
            $skus = $data['skus'] ?? ['TEST001', 'TEST002', 'TEST003'];

            $isToggleEnabled = $featureToggles->isEnabled(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING);

            $startTime = microtime(true);
            $result = $dao->get_skus_missing_class($skus);
            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);

            return $this->json([
                'success' => true,
                'data' => [
                    'method' => 'get_skus_missing_class',
                    'input_skus' => $skus,
                    'result' => $result,
                    'feature_toggle_enabled' => $isToggleEnabled,
                    'implementation_used' => $isToggleEnabled ? 'GraphQL' : 'SQL',
                    'execution_time_ms' => $executionTime,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => [
                    'message' => 'Failed to test GraphQL DAO',
                    'details' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            ], 500);
        }
    }

    /**
     * Test GraphQL service directly
     *
     * @Route(path="/debug_graphql_direct", methods={"POST"})
     * @param Request $request
     * @param CurationProductGraphService $graphService
     * @return JsonResponse
     */
    public function debug_graphql_direct(
        Request $request,
        CurationProductGraphService $graphService
    ): JsonResponse {
        try {
            $data = json_decode($request->getContent(), true);
            $skus = $data['skus'] ?? ['TEST001', 'TEST002', 'TEST003'];

            $startTime = microtime(true);

            // Test each GraphQL method directly
            $results = [];

            try {
                $results['get_skus_missing_class'] = [
                    'result' => $graphService->getSkusMissingClass($skus),
                    'success' => true
                ];
            } catch (\Exception $e) {
                $results['get_skus_missing_class'] = [
                    'error' => $e->getMessage(),
                    'success' => false
                ];
            }

            try {
                $results['get_skus_invalid_brand_catalog'] = [
                    'result' => $graphService->getSkusInvalidBrandCatalog($skus),
                    'success' => true
                ];
            } catch (\Exception $e) {
                $results['get_skus_invalid_brand_catalog'] = [
                    'error' => $e->getMessage(),
                    'success' => false
                ];
            }

            try {
                $results['get_skus_with_kit_data'] = [
                    'result' => $graphService->getSkusWithKitData($skus),
                    'success' => true
                ];
            } catch (\Exception $e) {
                $results['get_skus_with_kit_data'] = [
                    'error' => $e->getMessage(),
                    'success' => false
                ];
            }

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);

            return $this->json([
                'success' => true,
                'data' => [
                    'test_type' => 'Direct GraphQL Service Test',
                    'input_skus' => $skus,
                    'results' => $results,
                    'total_execution_time_ms' => $executionTime,
                    'timestamp' => date('Y-m-d H:i:s')
                ]
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => [
                    'message' => 'Failed to test GraphQL service directly',
                    'details' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            ], 500);
        }
    }

    /**
     * Compare SQL vs GraphQL results side by side
     *
     * @Route(path="/debug_sql_vs_graphql", methods={"POST"})
     * @param Request $request
     * @param Curation_Data_Consistency_Validation_DAO $dao
     * @param CurationProductGraphService $graphService
     * @param FeatureTogglesInterface $featureToggles
     * @return JsonResponse
     */
    public function debug_sql_vs_graphql(
        Request $request,
        Curation_Data_Consistency_Validation_DAO $dao,
        CurationProductGraphService $graphService,
        FeatureTogglesInterface $featureToggles
    ): JsonResponse {
        try {
            $data = json_decode($request->getContent(), true);
            $skus = $data['skus'] ?? ['TEST001', 'TEST002', 'TEST003'];
            $method = $data['method'] ?? 'get_skus_missing_class';

            $results = [
                'test_info' => [
                    'method_tested' => $method,
                    'input_skus' => $skus,
                    'timestamp' => date('Y-m-d H:i:s'),
                    'feature_toggle_status' => $featureToggles->isEnabled(FeatureToggle::CURATION_DATA_GRAPHQL_DECOUPLING)
                ]
            ];

            // Test SQL implementation (force SQL by temporarily disabling toggle logic)
            $sqlStartTime = microtime(true);
            try {
                switch ($method) {
                    case 'get_skus_missing_class':
                        // Force SQL by calling the method that bypasses toggle
                        $sqlResult = $this->forceSqlQuery($dao, 'get_skus_missing_class', $skus);
                        break;
                    case 'get_skus_invalid_brand_catalog':
                        $sqlResult = $this->forceSqlQuery($dao, 'get_skus_invalid_brand_catalog', $skus);
                        break;
                    case 'get_skus_with_kit_data':
                        $sqlResult = $this->forceSqlQuery($dao, 'get_skus_with_kit_data', $skus);
                        break;
                    default:
                        throw new \InvalidArgumentException("Unknown method: $method");
                }
                $sqlEndTime = microtime(true);

                $results['sql_implementation'] = [
                    'success' => true,
                    'result' => $sqlResult,
                    'execution_time_ms' => round(($sqlEndTime - $sqlStartTime) * 1000, 2),
                    'result_count' => is_array($sqlResult) ? count($sqlResult) : 0
                ];
            } catch (\Exception $e) {
                $results['sql_implementation'] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'execution_time_ms' => 0
                ];
            }

            // Test GraphQL implementation
            $graphqlStartTime = microtime(true);
            try {
                switch ($method) {
                    case 'get_skus_missing_class':
                        $graphqlResult = $graphService->getSkusMissingClass($skus);
                        break;
                    case 'get_skus_invalid_brand_catalog':
                        $graphqlResult = $graphService->getSkusInvalidBrandCatalog($skus);
                        break;
                    case 'get_skus_with_kit_data':
                        $graphqlResult = $graphService->getSkusWithKitData($skus);
                        break;
                    default:
                        throw new \InvalidArgumentException("Unknown method: $method");
                }
                $graphqlEndTime = microtime(true);

                $results['graphql_implementation'] = [
                    'success' => true,
                    'result' => $graphqlResult,
                    'execution_time_ms' => round(($graphqlEndTime - $graphqlStartTime) * 1000, 2),
                    'result_count' => is_array($graphqlResult) ? count($graphqlResult) : 0
                ];
            } catch (\Exception $e) {
                $results['graphql_implementation'] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'execution_time_ms' => 0
                ];
            }

            // Compare results
            $results['comparison'] = $this->compareResults($results);

            return $this->json([
                'success' => true,
                'data' => $results
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => [
                    'message' => 'Failed to compare SQL vs GraphQL',
                    'details' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]
            ], 500);
        }
    }

    /**
     * Force SQL query by using reflection to bypass toggle logic
     */
    private function forceSqlQuery($dao, string $method, array $skus)
    {
        // This is a hack to force SQL execution for comparison
        // In a real implementation, you might want to add a parameter to bypass the toggle
        $reflection = new \ReflectionClass($dao);
        $property = $reflection->getProperty('unleashToggle');
        $property->setAccessible(true);
        $originalToggle = $property->getValue($dao);

        // Create a mock toggle that always returns false
        $mockToggle = new class {
            public function isGraphQLDecouplingEnabled(): bool { return false; }
            public function getRolloutPercentage(): int { return 0; }
        };

        $property->setValue($dao, $mockToggle);

        try {
            $result = $dao->$method($skus);
        } finally {
            // Restore original toggle
            $property->setValue($dao, $originalToggle);
        }

        return $result;
    }

    /**
     * Compare SQL and GraphQL results
     */
    private function compareResults(array $results): array
    {
        $comparison = [
            'results_match' => false,
            'performance_comparison' => [],
            'differences' => []
        ];

        if ($results['sql_implementation']['success'] && $results['graphql_implementation']['success']) {
            $sqlResult = $results['sql_implementation']['result'];
            $graphqlResult = $results['graphql_implementation']['result'];

            // Compare results
            $comparison['results_match'] = $this->arraysEqual($sqlResult, $graphqlResult);

            if (!$comparison['results_match']) {
                $comparison['differences'] = [
                    'sql_count' => $results['sql_implementation']['result_count'],
                    'graphql_count' => $results['graphql_implementation']['result_count'],
                    'sql_sample' => array_slice($sqlResult, 0, 5),
                    'graphql_sample' => array_slice($graphqlResult, 0, 5)
                ];
            }

            // Performance comparison
            $sqlTime = $results['sql_implementation']['execution_time_ms'];
            $graphqlTime = $results['graphql_implementation']['execution_time_ms'];

            $comparison['performance_comparison'] = [
                'sql_time_ms' => $sqlTime,
                'graphql_time_ms' => $graphqlTime,
                'difference_ms' => $graphqlTime - $sqlTime,
                'graphql_faster' => $graphqlTime < $sqlTime,
                'performance_improvement' => $sqlTime > 0 ? round((($sqlTime - $graphqlTime) / $sqlTime) * 100, 2) : 0
            ];
        }

        return $comparison;
    }

    /**
     * Deep array comparison
     */
    private function arraysEqual($a, $b): bool
    {
        return serialize($a) === serialize($b);
    }
}
