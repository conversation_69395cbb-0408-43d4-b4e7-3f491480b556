<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\ProductManagement\Curation;

use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Connection\PostgresConnection;
use App\Infrastructure\Exception\ExecutionException;
use WF\Shared\Helpers\SQL;
use PDO;

class Curation_Collider_Postgres_DAO {

  private ProductConnection $pdo;

  private PostgresConnection $pdo_psql;

  /**
   * Curation_Collider_DAO constructor.
   *
   * @param ProductConnection $pdo pdo
   */
  public function __construct(ProductConnection $pdo, PostgresConnection $pdo_psql) {
    $this->pdo = $pdo;
    $this->pdo_psql = $pdo_psql;
  }

  /**
   * @param array $skus         a list of skus which are being checked. they will be in the result if they exist in the curation table
   *
   * @param bool  $ignoreKitsco if should ignore kitsco skus (which makes sense by default)
   *
   * @return array
   */
  public function getExistingCurationSKUS(array $skus, bool $ignoreKitsco = true) : array {
    $skus_lst  = $this->pdo_psql->paramsForLists($skus);
    if($skus_lst === ""){return array();}
    $sql = "
      SELECT vi.\"ViSKU\"
      FROM \"tblVerificationItem\" AS vi 
      WHERE
        vi.\"ViSKU\" IN ($skus_lst)
        AND vi.\"ViVerificationID\" IS NULL
    ";
    if ($ignoreKitsco) {
      $sql .= " AND vi.\"ViIsKitsco\" = false"; //consider kitsco items as if they never existed in the vi table
    }

    $statement = $this->pdo_psql->prepare($sql);
    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get existing curation SKUs: ' . implode(';', $statement->errorInfo()));
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }


  /**
   * @param string $sku SKU
   *
   * @return mixed
   */
  public function getProductCollection($sku) {
    $result =array();
    $productDetails= $this->getProductDetails($sku);
    $verificationDetails= $this->getVerificationDetails($sku);

    if(!empty($productDetails) && !empty($verificationDetails)){
      $result=array_merge($productDetails,$verificationDetails);
    }else if(!empty($productDetails)){
      $result=$productDetails;
    }else if(!empty($verificationDetails)){
      $result=$verificationDetails;
    }
    if(array_key_exists('ContextXnID',$result)){
      return $result['ContextXnID'];
    }elseif(array_key_exists('PrXnID',$result)){
      return $result['PrXnID'];
    }
    return null;
  }

  /**
   * @param string $sku SKU
   *
   * @return mixed
   */
  public function getProductDetails($sku){

    $sql='SELECT p."PrXnID", p."PrSKU"
          FROM "tblProduct" p
          WHERE p."PrSKU" = :sku';
    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);
    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get product collection - ' . implode(';', $statement->errorInfo()));
    }
    return $statement->fetch();
  }

  /**
   * @param string $sku SKU
   *
   * @return mixed
   */
  public function getVerificationDetails(string $sku){
    $sql='SELECT vr."ContextXnID",vr."ViSKU" FROM
          (SELECT "ViContextXnID" AS "ContextXnID","ViSKU",
	        ROW_NUMBER() OVER (PARTITION BY "ViSKU" ORDER BY "ViID" DESC) AS "RowNumber"
          FROM "tblVerificationItem" 
          WHERE "ViSKU" = :sku
    ) vr WHERE vr."RowNumber"=1';
    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);
    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get product collection - ' . implode(';', $statement->errorInfo()));
    }
    return $statement->fetch();
  }

  /**
   * @param array $possibleSKUs       the list of possible curated skus
   * @param int   $withinNumberOfDays the number of days to consider
   *
   * @return array
   */
  public function getRecentlyCuratedSKUs(array $possibleSKUs, int $withinNumberOfDays) : array {
    $in  = str_repeat('?,', count($possibleSKUs) - 1) . '?';
    $sql = "
      SELECT DISTINCT vi.\"ViSKU\"
      FROM \"tblVerificationItem\" vi 
      WHERE
        vi.\"ViVerificationID\" IS NULL
        AND vi.\"ViCreatedAt\" >= CURRENT_TIMESTAMP  + INTERVAL '". -$withinNumberOfDays ." day'
        AND vi.\"ViSKU\" IN ($in)
    ";

    $statement = $this->pdo_psql->prepare($sql);

    if (!$statement->execute($possibleSKUs)) {
      throw ExecutionException::forStatement($statement, 'Cannot get product collection');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }
}
