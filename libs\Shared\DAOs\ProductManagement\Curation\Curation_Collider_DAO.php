<?php
/**
 * PHP version 7
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2017 Wayfair LLC - All rights reserved
 */

namespace WF\Shared\DAOs\ProductManagement\Curation;

use App\Domain\Model\FeatureToggle;
use App\Infrastructure\Connection\ProductConnection;
use App\Infrastructure\Exception\ExecutionException;
use App\Infrastructure\External\FeatureToggle\FeatureTogglesInterface;
use WF\Shared\Helpers\SQL;
use WF\Shared\Merchandising\SKU_Collisions\Storage\SKU_Collider_DAO;
use PDO;

class Curation_Collider_DAO extends SKU_Collider_DAO {

  private ProductConnection $pdo;
  private ProductConnection $pdo_psql;
  private FeatureTogglesInterface $featureToggles;

  /**
   * Curation_Collider_DAO constructor.
   *
   * @param ProductConnection $pdo pdo
   * @param ProductConnection $pdo_psql PostgreSQL PDO
   * @param FeatureTogglesInterface $featureToggles Feature Toggles
   */
  public function __construct(ProductConnection $pdo, ProductConnection $pdo_psql, FeatureTogglesInterface $featureToggles) {
    $this->pdo = $pdo;
    $this->pdo_psql = $pdo_psql;
    $this->featureToggles = $featureToggles;
    parent::__construct($pdo);
  }

  /**
   * @param array $skus         a list of skus which are being checked. they will be in the result if they exist in the curation table
   *
   * @param bool  $ignoreKitsco if should ignore kitsco skus (which makes sense by default)
   *
   * @return array
   */
  public function getExistingCurationSKUS(array $skus, $ignoreKitsco = true) : array {
    $sql = '
      SELECT vi.ViSKU
      FROM csn_product.dbo.tblVerificationItem vi WITH (NOLOCK)
      WHERE
        vi.ViSKU IN ' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . '
        AND vi.ViVerificationID IS NULL
    ';
    if ($ignoreKitsco) {
      $sql .= 'AND vi.ViIsKitsco = 0'; //consider kitsco items as if they never existed in the vi table
    }

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));
    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get existing curation SKUs: ' . implode(';', $statement->errorInfo()));
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param array $skus Skus
   *
   * @return array
   */
  public function getKitChildren(array $skus) : array {
    $sql = '
      SELECT DISTINCT component.ChildSKU
      FROM csn_product.dbo.vwExclusivityKitCompositionActive component WITH (NOLOCK)
      WHERE component.ParentSKU IN ' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . '
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));
    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get kit children: ' . implode(';', $statement->errorInfo()));
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }

  /**
   * @param array $skus Skus
   *
   * @return array
   */
  public function getKitParents(array $skus) : array {
    $sql = '
      SELECT DISTINCT component.ParentSku
      FROM csn_product.dbo.vwExclusivityKitCompositionActive component WITH (NOLOCK)
      WHERE component.ChildSKU IN ' . $this->pdo->paramsForList(count($skus), 'sku', SQL::nvarchar(8)) . '
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('sku', $skus, SQL::nvarchar(8));
    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get kit parents: ' . implode(';', $statement->errorInfo()));
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }


  /**
   * @param string $sku SKU
   *
   * @return mixed
   */
  public function getProductCollection($sku) {
    $enabledNewQuery=true;
    // we will remove this condition, when we add feature toggle
    if($enabledNewQuery){
      $result =array();
      $productDetails= $this->getProductDetails($sku);
      $verificationDetails= $this->getVerificationDetails($sku);

      if(!empty($productDetails) && !empty($verificationDetails)){
        $result=array_merge($productDetails,$verificationDetails);
      }else if(!empty($productDetails)){
        $result=$productDetails;
      }else if(!empty($verificationDetails)){
        $result=$verificationDetails;
      }
      if(array_key_exists('ContextXnID',$result)){
        return $result['ContextXnID'];
      }elseif(array_key_exists('PrXnID',$result)){
        return $result['PrXnID'];
      }
      return null;
    }else{
      if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
        return $this->getProductCollectionFallback_psql($sku);
      } else {
        return $this->getProductCollectionFallback_sql($sku);
      }
    }
  }

  /**
   * Fallback method for getting product collection (SQL Server version)
   *
   * @param string $sku SKU
   *
   * @return mixed
   */
  private function getProductCollectionFallback_sql($sku) {
    $sql = '
          SELECT ISNULL(curation.ContextXnID, p.PrXnID) AS collection
          FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
          OUTER APPLY (
            SELECT TOP 1 v.ViContextXnID AS ContextXnID
            FROM csn_product.dbo.tblVerificationItem v WITH(NOLOCK)
            WHERE v.ViSKU = p.PrSKU
            ORDER BY v.ViID DESC
          ) AS curation
          WHERE p.PrSKU = :sku';
    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get product collection - ' . implode(';', $statement->errorInfo()));
    }
    return $statement->fetchColumn();
  }

  /**
   * Fallback method for getting product collection (PostgreSQL version)
   *
   * @param string $sku SKU
   *
   * @return mixed
   */
  private function getProductCollectionFallback_psql($sku) {
    $sql = '
          SELECT COALESCE(curation."ContextXnID", p."PrXnID") AS collection
          FROM "tblProduct" p
          LEFT JOIN LATERAL (
            SELECT v."ViContextXnID" AS "ContextXnID"
            FROM "tblVerificationItem" v
            WHERE v."ViSKU" = p."PrSKU"
            ORDER BY v."ViID" DESC
            LIMIT 1
          ) AS curation ON true
          WHERE p."PrSKU" = :sku';
    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);

    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get product collection from PostgreSQL - ' . implode(';', $statement->errorInfo()));
    }
    return $statement->fetchColumn();
  }

  /**
   * @param string $sku SKU
   *
   * @return mixed
   */
  public function getProductDetails($sku){
    if ($this->featureToggles->isEnabled(FeatureToggle::CURATION_DATA_DECOUPLING_BATCH_MGMT)) {
      return $this->getProductDetails_psql($sku);
    } else {
      return $this->getProductDetails_sql($sku);
    }
  }

  /**
   * @param string $sku SKU
   *
   * @return mixed
   */
  private function getProductDetails_sql($sku){
    $sql='SELECT p.PrXnID,p.PrSKU
          FROM csn_product.dbo.tblProduct p WITH (NOLOCK)
          WHERE p.PrSKU = :sku';
    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);
    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get product collection - ' . implode(';', $statement->errorInfo()));
    }
    return $statement->fetch();
  }

  /**
   * @param string $sku SKU
   *
   * @return mixed
   */
  private function getProductDetails_psql($sku){
    $sql='SELECT p."PrXnID", p."PrSKU"
          FROM "tblProduct" p
          WHERE p."PrSKU" = :sku';
    $statement = $this->pdo_psql->prepare($sql);
    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);
    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get product collection from PostgreSQL - ' . implode(';', $statement->errorInfo()));
    }
    return $statement->fetch();
  }

  /**
   * @param string $sku SKU
   *
   * @return mixed
   */
  public function getVerificationDetails($sku){
    $sql='SELECT vr.ContextXnID,vr.ViSKU FROM
          (SELECT ViContextXnID AS ContextXnID,ViSKU,
	        ROW_NUMBER() OVER (PARTITION BY ViSKU ORDER BY ViID DESC) AS RowNumber
          FROM csn_product.dbo.tblVerificationItem WITH(NOLOCK)
          WHERE ViSKU = :sku
    ) vr WHERE vr.RowNumber=1';
    $statement = $this->pdo->prepare($sql);
    $statement->bindValue(':sku', $sku, PDO::PARAM_STR);
    if (!$statement->execute()) {
      throw new ExecutionException('Cannot get product collection - ' . implode(';', $statement->errorInfo()));
    }
    return $statement->fetch();
  }

  /**
   * @param array $possibleSKUs       the list of possible curated skus
   * @param int   $withinNumberOfDays the number of days to consider
   *
   * @return array
   */
  public function getRecentlyCuratedSKUs(array $possibleSKUs, int $withinNumberOfDays) : array {
    $sql = '
      SELECT DISTINCT vi.ViSKU
      FROM csn_product.dbo.tblVerificationItem vi WITH ( NOLOCK )
      WHERE
        vi.ViVerificationID IS NULL
        AND vi.ViCreatedAt >= DATEADD(DAY, :days, SYSDATETIMEOFFSET())
        AND vi.ViSKU IN ' . $this->pdo->paramsForList(count($possibleSKUs), 'skus', SQL::nvarchar(8)) . '
    ';

    $statement = $this->pdo->prepare($sql);
    $statement->bindValuesList('skus', $possibleSKUs, SQL::nvarchar(8));
    $statement->bindValue('days', -$withinNumberOfDays, PDO::PARAM_INT);

    if (!$statement->execute()) {
      throw ExecutionException::forStatement($statement, 'Cannot get product collection');
    }

    return $statement->fetchAll(PDO::FETCH_COLUMN);
  }
}
