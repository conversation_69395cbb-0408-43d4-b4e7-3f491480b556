<?php

declare(strict_types=1);

/**
 * Simple GraphQL toggle test controller following existing pattern
 * 
 * <AUTHOR> for MTBW-4912
 * @copyright 2024 Wayfair LLC - All rights reserved
 */

namespace App\Application\Controller;

use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class GraphQLToggleTestController extends AbstractBaseController
{
    /**
     * Test endpoint to verify GraphQL toggle functionality
     * 
     * @Route(path="/graphql_toggle_test", methods={"GET"})
     * @return JsonResponse
     */
    public function graphql_toggle_test(): JsonResponse
    {
        try {
            // Simple test without dependencies first
            $response = [
                'success' => true,
                'message' => 'GraphQL toggle test endpoint is working',
                'timestamp' => date('Y-m-d H:i:s'),
                'environment' => $_ENV['WF_ENV'] ?? 'unknown',
                'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
                'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
            ];

            return $this->json($response);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test endpoint with POST method
     * 
     * @Route(path="/graphql_toggle_post_test", methods={"POST"})
     * @return JsonResponse
     */
    public function graphql_toggle_post_test(): JsonResponse
    {
        try {
            $response = [
                'success' => true,
                'message' => 'GraphQL toggle POST test endpoint is working',
                'timestamp' => date('Y-m-d H:i:s'),
                'method' => 'POST'
            ];

            return $this->json($response);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
